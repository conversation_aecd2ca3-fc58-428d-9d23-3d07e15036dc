package top.auunes.demo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;

/**
 * 文章标签关联实体类
 * 对应数据库中的article_tag表，用于存储文章和标签的多对多关联关系
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@TableName("article_tag") // MyBatis-Plus注解，指定对应的数据库表名为article_tag
public class ArticleTagEntity {

    /**
     * 关联关系唯一标识ID
     * 主键字段，自增长
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 文章ID
     */
    private Long articleId;

    /**
     * 标签ID
     */
    private Long tagId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 默认构造函数
     */
    public ArticleTagEntity() {
    }

    /**
     * 构造函数
     *
     * @param articleId 文章ID
     * @param tagId 标签ID
     */
    public ArticleTagEntity(Long articleId, Long tagId) {
        this.articleId = articleId;
        this.tagId = tagId;
        this.createTime = LocalDateTime.now();
    }

    // Getter和Setter方法

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getArticleId() {
        return articleId;
    }

    public void setArticleId(Long articleId) {
        this.articleId = articleId;
    }

    public Long getTagId() {
        return tagId;
    }

    public void setTagId(Long tagId) {
        this.tagId = tagId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "ArticleTagEntity{" +
                "id=" + id +
                ", articleId=" + articleId +
                ", tagId=" + tagId +
                ", createTime=" + createTime +
                '}';
    }
}