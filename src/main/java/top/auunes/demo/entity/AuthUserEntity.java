package top.auunes.demo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;

/**
 * 认证用户实体类
 * 对应数据库中的auth_user表，用于JWT认证系统
 * 独立于原有的sys_user表
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@TableName("user") // MyBatis-Plus注解，指定对应的数据库表名为auth_user
public class AuthUserEntity {

    /**
     * 用户唯一标识ID
     * 主键字段，自增长
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户名
     * 用于用户登录的唯一标识，不能重复
     */
    private String username;

    /**
     * 用户密码
     * 存储MD5加密后的密码
     */
    private String password;

    /**
     * 用户昵称
     * 用户的显示名称
     */
    private String nickname;

    /**
     * 用户头像URL
     * 用户头像的访问地址
     */
    private String avatar;

    /**
     * 用户状态
     * 1-启用，0-禁用
     */
    private Integer status;

    /**
     * 创建时间
     * 用户注册时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     * 用户信息最后更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 默认构造函数
     */
    public AuthUserEntity() {
    }

    /**
     * 获取用户ID
     * 
     * @return Long 用户ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置用户ID
     * 
     * @param id 用户ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取用户名
     * 
     * @return String 用户名
     */
    public String getUsername() {
        return username;
    }

    /**
     * 设置用户名
     * 
     * @param username 用户名
     */
    public void setUsername(String username) {
        this.username = username;
    }

    /**
     * 获取用户密码
     * 
     * @return String 用户密码（MD5加密后）
     */
    public String getPassword() {
        return password;
    }

    /**
     * 设置用户密码
     * 
     * @param password 用户密码（MD5加密后）
     */
    public void setPassword(String password) {
        this.password = password;
    }

    /**
     * 获取用户昵称
     * 
     * @return String 用户昵称
     */
    public String getNickname() {
        return nickname;
    }

    /**
     * 设置用户昵称
     * 
     * @param nickname 用户昵称
     */
    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    /**
     * 获取用户头像URL
     * 
     * @return String 用户头像URL
     */
    public String getAvatar() {
        return avatar;
    }

    /**
     * 设置用户头像URL
     * 
     * @param avatar 用户头像URL
     */
    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    /**
     * 获取用户状态
     * 
     * @return Integer 用户状态（1-启用，0-禁用）
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置用户状态
     * 
     * @param status 用户状态（1-启用，0-禁用）
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取创建时间
     * 
     * @return LocalDateTime 创建时间
     */
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     * 
     * @param createTime 创建时间
     */
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     * 
     * @return LocalDateTime 更新时间
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     * 
     * @param updateTime 更新时间
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 重写toString方法
     * 用于对象的字符串表示，便于调试和日志输出
     * 注意：为了安全考虑，不输出密码信息
     * 
     * @return String 对象的字符串表示
     */
    @Override
    public String toString() {
        return "AuthUserEntity{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", password='[HIDDEN]'" +
                ", nickname='" + nickname + '\'' +
                ", avatar='" + avatar + '\'' +
                ", status=" + status +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
