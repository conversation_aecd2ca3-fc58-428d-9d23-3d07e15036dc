package top.auunes.demo.entity;


import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 系统用户实体类
 * 对应数据库中的sys_user表，用于存储用户基本信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@TableName("sys_user") // MyBatis-Plus注解，指定对应的数据库表名为sys_user
public class SysUserEntity {

    /**
     * 用户唯一标识ID
     * 主键字段，使用雪花算法生成
     */
    private Long id;

    /**
     * 用户登录名
     * 用于用户登录的唯一标识，不能重复
     */
    private String LoginName;

    /**
     * 用户真实姓名
     * 用户的显示名称
     */
    private String name;

    /**
     * 用户密码
     * 存储MD5加密后的密码
     */
    private String password;

    /**
     * 获取用户ID
     *
     * @return Long 用户ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置用户ID
     *
     * @param id 用户ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取用户登录名
     *
     * @return String 用户登录名
     */
    public String getLoginName() {
        return LoginName;
    }

    /**
     * 设置用户登录名
     *
     * @param loginName 用户登录名
     */
    public void setLoginName(String loginName) {
        LoginName = loginName;
    }

    /**
     * 获取用户真实姓名
     *
     * @return String 用户真实姓名
     */
    public String getName() {
        return name;
    }

    /**
     * 设置用户真实姓名
     *
     * @param name 用户真实姓名
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取用户密码
     *
     * @return String 用户密码（MD5加密后）
     */
    public String getPassword() {
        return password;
    }

    /**
     * 设置用户密码
     *
     * @param password 用户密码（MD5加密后）
     */
    public void setPassword(String password) {
        this.password = password;
    }

    /**
     * 重写toString方法
     * 用于对象的字符串表示，便于调试和日志输出
     *
     * @return String 对象的字符串表示
     */
    @Override
    public String toString() {
        return "SysUserEntity{" +
                "id=" + id +
                ", LoginName='" + LoginName + '\'' +
                ", name='" + name + '\'' +
                ", password='" + password + '\'' +
                '}';
    }
}
