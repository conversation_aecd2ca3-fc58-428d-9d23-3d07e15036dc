package top.auunes.demo.mapper;

// 导入MyBatis-Plus的基础Mapper接口
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
// 导入MyBatis的Mapper注解
import org.apache.ibatis.annotations.Mapper;
// 导入文章实体类
import top.auunes.demo.entity.ArticleEntity;

/**
 * 文章数据访问层接口
 * 继承MyBatis-Plus的BaseMapper接口，提供基础的CRUD操作
 * 专门处理article表的数据操作
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Mapper // MyBatis注解，标识这是一个Mapper接口，Spring会自动创建代理实现
public interface ArticleMapper extends BaseMapper<ArticleEntity> {

    // 继承BaseMapper后，自动拥有以下方法：
    // - insert(T entity): 插入一条记录
    // - deleteById(Serializable id): 根据ID删除
    // - updateById(T entity): 根据ID更新
    // - selectById(Serializable id): 根据ID查询
    // - selectList(Wrapper<T> queryWrapper): 根据条件查询列表
    // - selectPage(Page<T> page, Wrapper<T> queryWrapper): 分页查询
    // 等等...

    // 如果需要自定义SQL查询，可以在这里添加方法声明
    // 例如：
    // /**
    //  * 根据分类ID查询文章列表
    //  * @param categoryId 分类ID
    //  * @return List<ArticleEntity> 文章列表
    //  */
    // List<ArticleEntity> selectByCategoryId(Long categoryId);

    // /**
    //  * 根据作者ID和状态查询文章列表
    //  * @param authorId 作者ID
    //  * @param status 文章状态
    //  * @return List<ArticleEntity> 文章列表
    //  */
    // List<ArticleEntity> selectByAuthorIdAndStatus(Long authorId, String status);

    // /**
    //  * 增加文章阅读次数
    //  * @param articleId 文章ID
    //  * @return int 影响行数
    //  */
    // int incrementViewCount(Long articleId);

    // /**
    //  * 增加文章点赞次数
    //  * @param articleId 文章ID
    //  * @return int 影响行数
    //  */
    // int incrementLikeCount(Long articleId);
}