package top.auunes.demo.mapper;

// 导入MyBatis-Plus的基础Mapper接口
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
// 导入MyBatis的Mapper注解
import org.apache.ibatis.annotations.Mapper;
// 导入文章标签关联实体类
import top.auunes.demo.entity.ArticleTagEntity;

/**
 * 文章标签关联数据访问层接口
 * 继承MyBatis-Plus的BaseMapper接口，提供基础的CRUD操作
 * 专门处理article_tag表的数据操作
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Mapper // MyBatis注解，标识这是一个Mapper接口，Spring会自动创建代理实现
public interface ArticleTagMapper extends BaseMapper<ArticleTagEntity> {

    // 继承BaseMapper后，自动拥有以下方法：
    // - insert(T entity): 插入一条记录
    // - deleteById(Serializable id): 根据ID删除
    // - updateById(T entity): 根据ID更新
    // - selectById(Serializable id): 根据ID查询
    // - selectList(Wrapper<T> queryWrapper): 根据条件查询列表
    // 等等...

    // 如果需要自定义SQL查询，可以在这里添加方法声明
    // 例如：
    // /**
    //  * 根据文章ID查询关联的标签ID列表
    //  * @param articleId 文章ID
    //  * @return List<Long> 标签ID列表
    //  */
    // List<Long> selectTagIdsByArticleId(Long articleId);

    // /**
    //  * 根据标签ID查询关联的文章ID列表
    //  * @param tagId 标签ID
    //  * @return List<Long> 文章ID列表
    //  */
    // List<Long> selectArticleIdsByTagId(Long tagId);

    // /**
    //  * 根据文章ID删除所有关联关系
    //  * @param articleId 文章ID
    //  * @return int 影响行数
    //  */
    // int deleteByArticleId(Long articleId);

    // /**
    //  * 根据标签ID删除所有关联关系
    //  * @param tagId 标签ID
    //  * @return int 影响行数
    //  */
    // int deleteByTagId(Long tagId);

    // /**
    //  * 批量插入文章标签关联关系
    //  * @param articleTagList 关联关系列表
    //  * @return int 影响行数
    //  */
    // int batchInsert(List<ArticleTagEntity> articleTagList);
}