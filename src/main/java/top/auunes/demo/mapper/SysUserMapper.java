package top.auunes.demo.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import top.auunes.demo.entity.SysUserEntity;


/**
 * 系统用户数据访问层接口
 * 继承MyBatis-Plus的BaseMapper接口，提供基础的CRUD操作
 * 无需编写SQL语句，MyBatis-Plus会自动生成常用的数据库操作方法
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Mapper // MyBatis注解，标识这是一个Mapper接口，Spring会自动创建代理实现
public interface SysUserMapper extends BaseMapper<SysUserEntity> {
    // 继承BaseMapper后，自动拥有以下方法：
    // - insert(T entity): 插入一条记录
    // - deleteById(Serializable id): 根据ID删除
    // - updateById(T entity): 根据ID更新
    // - selectById(Serializable id): 根据ID查询
    // - selectList(Wrapper<T> queryWrapper): 根据条件查询列表
    // 等等...

    // 如果需要自定义SQL查询，可以在这里添加方法声明
    // 例如：List<SysUserEntity> selectByCustomCondition(String condition);
}
