package top.auunes.demo.mapper;

// 导入MyBatis-Plus的基础Mapper接口
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
// 导入MyBatis的Mapper注解
import org.apache.ibatis.annotations.Mapper;
// 导入分类实体类
import top.auunes.demo.entity.CategoryEntity;

/**
 * 分类数据访问层接口
 * 继承MyBatis-Plus的BaseMapper接口，提供基础的CRUD操作
 * 专门处理category表的数据操作
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Mapper // MyBatis注解，标识这是一个Mapper接口，Spring会自动创建代理实现
public interface CategoryMapper extends BaseMapper<CategoryEntity> {

    // 继承BaseMapper后，自动拥有以下方法：
    // - insert(T entity): 插入一条记录
    // - deleteById(Serializable id): 根据ID删除
    // - updateById(T entity): 根据ID更新
    // - selectById(Serializable id): 根据ID查询
    // - selectList(Wrapper<T> queryWrapper): 根据条件查询列表
    // 等等...

    // 如果需要自定义SQL查询，可以在这里添加方法声明
    // 例如：
    // /**
    //  * 根据排序值查询分类列表
    //  * @return List<CategoryEntity> 分类列表（按排序值升序）
    //  */
    // List<CategoryEntity> selectAllOrderBySortOrder();

    // /**
    //  * 更新分类的文章数量
    //  * @param categoryId 分类ID
    //  * @param count 文章数量
    //  * @return int 影响行数
    //  */
    // int updateArticleCount(Long categoryId, Integer count);
}