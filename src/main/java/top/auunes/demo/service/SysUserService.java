package top.auunes.demo.service;


import top.auunes.demo.req.SysUserLoginReq;
import top.auunes.demo.req.SysUserSaveReq;
import top.auunes.demo.resp.SysUserLoginResp;

/**
 * 系统用户服务接口
 * 定义用户相关的业务操作方法
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface SysUserService {

    /**
     * 用户注册方法
     * 处理用户注册业务逻辑
     *
     * @param req 用户注册请求对象，包含用户注册信息
     */
    void register(SysUserSaveReq req);

    /**
     * 用户登录方法
     * 验证用户登录信息并返回登录结果
     *
     * @param req 用户登录请求对象，包含登录名和密码
     * @return SysUserLoginResp 登录响应对象，包含用户信息
     */
    SysUserLoginResp login(SysUserLoginReq req);
}
