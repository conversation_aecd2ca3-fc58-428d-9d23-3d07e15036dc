package top.auunes.demo.service;

// 导入文章相关的请求和响应对象
import top.auunes.demo.req.ArticleCreateReq;
import top.auunes.demo.req.ArticleUpdateReq;
import top.auunes.demo.resp.ArticleDetailResp;

/**
 * 文章服务接口
 * 定义文章相关的业务操作方法
 * 根据接口文档实现博客文章管理功能
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface ArticleService {

    /**
     * 创建文章
     * 根据请求参数创建新文章，包括处理标签关联
     *
     * @param req 文章创建请求对象
     * @param authorId 作者ID（从JWT token中获取）
     * @return Long 创建成功的文章ID
     * @throws RuntimeException 当参数验证失败或创建失败时抛出异常
     */
    Long createArticle(ArticleCreateReq req, Long authorId);

    /**
     * 获取文章详情
     * 根据文章ID获取文章的详细信息，包括分类、标签、作者等信息
     * 同时增加文章的阅读次数
     *
     * @param articleId 文章ID
     * @return ArticleDetailResp 文章详情响应对象
     * @throws RuntimeException 当文章不存在时抛出异常
     */
    ArticleDetailResp getArticleDetail(Long articleId);

    /**
     * 更新文章
     * 根据请求参数更新文章信息，包括处理标签关联的变更
     *
     * @param req 文章更新请求对象
     * @param authorId 作者ID（从JWT token中获取，用于权限验证）
     * @return Boolean 更新是否成功
     * @throws RuntimeException 当文章不存在、无权限或更新失败时抛出异常
     */
    Boolean updateArticle(ArticleUpdateReq req, Long authorId);

    /**
     * 删除文章
     * 根据文章ID删除文章，同时删除相关的标签关联关系
     *
     * @param articleId 文章ID
     * @param authorId 作者ID（从JWT token中获取，用于权限验证）
     * @return Boolean 删除是否成功
     * @throws RuntimeException 当文章不存在、无权限或删除失败时抛出异常
     */
    Boolean deleteArticle(Long articleId, Long authorId);

    /**
     * 发布文章
     * 将草稿状态的文章发布为已发布状态
     *
     * @param articleId 文章ID
     * @param authorId 作者ID（从JWT token中获取，用于权限验证）
     * @return Boolean 发布是否成功
     * @throws RuntimeException 当文章不存在、无权限或发布失败时抛出异常
     */
    Boolean publishArticle(Long articleId, Long authorId);

    /**
     * 取消发布文章
     * 将已发布状态的文章改为草稿状态
     *
     * @param articleId 文章ID
     * @param authorId 作者ID（从JWT token中获取，用于权限验证）
     * @return Boolean 取消发布是否成功
     * @throws RuntimeException 当文章不存在、无权限或操作失败时抛出异常
     */
    Boolean unpublishArticle(Long articleId, Long authorId);

    /**
     * 点赞文章
     * 增加文章的点赞次数
     *
     * @param articleId 文章ID
     * @return Boolean 点赞是否成功
     * @throws RuntimeException 当文章不存在时抛出异常
     */
    Boolean likeArticle(Long articleId);

    /**
     * 检查文章权限
     * 验证当前用户是否有权限操作指定文章
     *
     * @param articleId 文章ID
     * @param authorId 作者ID
     * @return Boolean 是否有权限
     */
    Boolean checkArticlePermission(Long articleId, Long authorId);
}