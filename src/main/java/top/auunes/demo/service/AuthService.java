package top.auunes.demo.service;

// 导入认证相关的请求和响应对象
import top.auunes.demo.req.AuthLoginReq;
import top.auunes.demo.resp.AuthLoginResp;

/**
 * 认证服务接口
 * 定义用户认证相关的业务操作方法
 * 独立于原有的用户服务，专门处理JWT认证业务
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface AuthService {
    
    /**
     * 用户登录方法
     * 验证用户登录信息并生成JWT Token
     * 
     * @param req 认证登录请求对象，包含用户名和密码
     * @return AuthLoginResp 登录响应对象，包含JWT Token和用户信息
     * @throws RuntimeException 当用户名或密码错误时抛出业务异常
     */
    AuthLoginResp login(AuthLoginReq req);

    /**
     * 用户登出方法
     * 处理用户登出业务逻辑
     * 注意：JWT是无状态的，通常登出由前端处理（删除本地token）
     * 如果需要实现token黑名单功能，可以在此方法中实现
     */
    void logout();

    /**
     * 根据用户名查询用户信息
     * 内部方法，用于登录验证
     * 
     * @param username 用户名
     * @return AuthUserEntity 用户实体对象，如果不存在返回null
     */
    // AuthUserEntity findByUsername(String username); // 可以根据需要添加
}
