package top.auunes.demo.service;

// 导入分类相关的请求和响应对象
import top.auunes.demo.req.CategoryCreateReq;
import top.auunes.demo.req.CategoryListReq;
import top.auunes.demo.req.CategoryUpdateReq;
import top.auunes.demo.resp.CategoryResp;
import top.auunes.demo.resp.PageResp;

import java.util.List;

/**
 * 分类服务接口
 * 定义分类相关的业务操作方法
 * 根据接口文档实现分类管理功能
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface CategoryService {

    /**
     * 创建分类
     * 根据请求参数创建新分类
     *
     * @param req 分类创建请求对象
     * @return Long 创建成功的分类ID
     * @throws RuntimeException 当参数验证失败或创建失败时抛出异常
     */
    Long createCategory(CategoryCreateReq req);

    /**
     * 获取所有分类列表
     * 获取所有分类信息，按排序值升序排列（不分页）
     * 对应接口：GET /api/category/list/all
     *
     * @return List<CategoryResp> 分类列表
     */
    List<CategoryResp> getAllCategories();

    /**
     * 分页获取分类列表
     * 根据条件分页查询分类信息
     * 对应接口：POST /api/category/list
     *
     * @param req 分类列表查询请求对象
     * @return PageResp<CategoryResp> 分页分类列表
     */
    PageResp<CategoryResp> getCategoryList(CategoryListReq req);

    /**
     * 更新分类
     * 根据请求参数更新分类信息
     *
     * @param req 分类更新请求对象
     * @return Boolean 更新是否成功
     * @throws RuntimeException 当参数验证失败或更新失败时抛出异常
     */
    Boolean updateCategory(CategoryUpdateReq req);

    /**
     * 获取分类详情
     * 根据分类ID获取分类的详细信息
     *
     * @param categoryId 分类ID
     * @return CategoryResp 分类详情响应对象
     * @throws RuntimeException 当分类不存在时抛出异常
     */
    CategoryResp getCategoryDetail(Long categoryId);

    /**
     * 删除分类
     * 根据分类ID删除分类
     * 注意：如果分类下有文章，则不允许删除
     *
     * @param categoryId 分类ID
     * @return Boolean 删除是否成功
     * @throws RuntimeException 当分类不存在、有关联文章或删除失败时抛出异常
     */
    Boolean deleteCategory(Long categoryId);

    /**
     * 检查分类名称是否已存在
     * 用于创建和更新时的重复性验证
     *
     * @param name 分类名称
     * @param excludeId 排除的分类ID（更新时使用，可为null）
     * @return Boolean 名称是否已存在
     */
    Boolean isCategoryNameExists(String name, Long excludeId);

    /**
     * 更新分类的文章数量
     * 当文章的分类发生变化时，更新相关分类的文章统计数量
     *
     * @param categoryId 分类ID
     */
    void updateCategoryArticleCount(Long categoryId);

    /**
     * 获取热门分类列表
     * 根据文章数量倒序获取热门分类
     *
     * @param limit 限制数量
     * @return List<CategoryResp> 热门分类列表
     */
    List<CategoryResp> getHotCategories(Integer limit);
}