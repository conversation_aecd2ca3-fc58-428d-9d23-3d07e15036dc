package top.auunes.demo.service;

// 导入标签相关的请求和响应对象
import top.auunes.demo.req.TagCreateReq;
import top.auunes.demo.req.TagListReq;
import top.auunes.demo.req.TagUpdateReq;
import top.auunes.demo.resp.TagResp;
import top.auunes.demo.resp.PageResp;

import java.util.List;

/**
 * 标签服务接口
 * 定义标签相关的业务操作方法
 * 根据接口文档实现标签管理功能
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface TagService {

    /**
     * 创建标签
     * 根据请求参数创建新标签
     *
     * @param req 标签创建请求对象
     * @return Long 创建成功的标签ID
     * @throws RuntimeException 当参数验证失败或创建失败时抛出异常
     */
    Long createTag(TagCreateReq req);

    /**
     * 获取所有标签列表
     * 获取所有标签信息，按创建时间升序排列（不分页）
     * 对应接口：GET /api/tag/list/all
     *
     * @return List<TagResp> 标签列表
     */
    List<TagResp> getAllTags();

    /**
     * 分页获取标签列表
     * 根据条件分页查询标签信息
     * 对应接口：POST /api/tag/list
     *
     * @param req 标签列表查询请求对象
     * @return PageResp<TagResp> 分页标签列表
     */
    PageResp<TagResp> getTagList(TagListReq req);

    /**
     * 更新标签
     * 根据请求参数更新标签信息
     *
     * @param req 标签更新请求对象
     * @return Boolean 更新是否成功
     * @throws RuntimeException 当参数验证失败或更新失败时抛出异常
     */
    Boolean updateTag(TagUpdateReq req);

    /**
     * 获取标签详情
     * 根据标签ID获取标签的详细信息
     *
     * @param tagId 标签ID
     * @return TagResp 标签详情响应对象
     * @throws RuntimeException 当标签不存在时抛出异常
     */
    TagResp getTagDetail(Long tagId);

    /**
     * 删除标签
     * 根据标签ID删除标签
     * 注意：如果标签下有文章，则不允许删除
     *
     * @param tagId 标签ID
     * @return Boolean 删除是否成功
     * @throws RuntimeException 当标签不存在、有关联文章或删除失败时抛出异常
     */
    Boolean deleteTag(Long tagId);

    /**
     * 检查标签名称是否已存在
     * 用于创建和更新时的重复性验证
     *
     * @param name 标签名称
     * @param excludeId 排除的标签ID（更新时使用，可为null）
     * @return Boolean 名称是否已存在
     */
    Boolean isTagNameExists(String name, Long excludeId);

    /**
     * 更新标签的文章数量
     * 当文章的标签发生变化时，更新相关标签的文章统计数量
     *
     * @param tagId 标签ID
     */
    void updateTagArticleCount(Long tagId);

    /**
     * 获取热门标签列表
     * 根据文章数量倒序获取热门标签
     *
     * @param limit 限制数量
     * @return List<TagResp> 热门标签列表
     */
    List<TagResp> getHotTags(Integer limit);

    /**
     * 批量删除标签
     * 根据标签ID列表批量删除标签
     * 注意：如果任何标签下有文章，则不允许删除
     *
     * @param tagIds 标签ID列表
     * @return Boolean 删除是否成功
     * @throws RuntimeException 当有标签不存在、有关联文章或删除失败时抛出异常
     */
    Boolean batchDeleteTags(List<Long> tagIds);

    /**
     * 根据文章ID获取标签列表
     * 用于获取某篇文章关联的所有标签
     *
     * @param articleId 文章ID
     * @return List<TagResp> 标签列表
     */
    List<TagResp> getTagsByArticleId(Long articleId);

    /**
     * 为文章设置标签
     * 先删除文章的所有标签关联，再添加新的标签关联
     *
     * @param articleId 文章ID
     * @param tagIds 标签ID列表
     */
    void setArticleTags(Long articleId, List<Long> tagIds);
}
