package top.auunes.demo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import top.auunes.demo.entity.SysUserEntity;
import top.auunes.demo.mapper.SysUserMapper;
import top.auunes.demo.req.SysUserLoginReq;
import top.auunes.demo.req.SysUserSaveReq;
import top.auunes.demo.resp.SysUserLoginResp;
import top.auunes.demo.service.SysUserService;
import top.auunes.demo.utils.CopyUtil;
import top.auunes.demo.utils.SnowFlake;


import javax.annotation.Resource;
import java.util.List;

/**
 * 系统用户服务实现类
 * 实现用户相关的业务逻辑，包括用户注册和登录
 * 继承MyBatis-Plus的ServiceImpl基类，获得基础的CRUD操作能力
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Service // Spring服务注解，标识这是一个服务层组件
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUserEntity> implements SysUserService {

    /**
     * 用户数据访问层对象
     * 使用@Resource注解注入，用于执行数据库操作
     */
    @Resource // Java标准注解，按名称注入依赖
    private SysUserMapper sysUserMapper;

    /**
     * 雪花算法ID生成器
     * 用于生成唯一的用户ID
     */
    @Autowired // Spring自动装配注解，按类型注入依赖
    private SnowFlake snowFlake;

    /**
     * 用户注册方法实现
     * 处理用户注册业务逻辑，包括用户名重复检查和用户信息保存
     *
     * @param req 用户注册请求对象，包含用户注册信息
     */
    @Override // 重写接口方法
    public void register(SysUserSaveReq req) {
        // 使用工具类将请求对象转换为实体对象
        SysUserEntity user = CopyUtil.copy(req, SysUserEntity.class);

        // 判断是否为新用户注册（ID为空表示新用户）
        if (ObjectUtils.isEmpty(req.getId())) {
            // 根据登录名查询数据库，检查用户名是否已存在
            SysUserEntity userDb = selectByLoginName(req.getLoginName());

            // 如果用户名不存在，则可以注册
            if (ObjectUtils.isEmpty(userDb)) {
                // 使用雪花算法生成唯一ID
                user.setId(snowFlake.nextId());
                // 将用户信息插入数据库
                sysUserMapper.insert(user);
            }
            // 注意：如果用户名已存在，这里没有处理逻辑，实际项目中应该抛出异常或返回错误信息
        }
    }

    /**
     * 用户登录方法实现
     * 验证用户登录信息并返回用户数据
     * 包含用户存在性检查和密码验证
     *
     * @param req 用户登录请求对象，包含登录名和加密后的密码
     * @return SysUserLoginResp 登录响应对象，包含用户信息；如果登录失败返回null
     */
    @Override // 重写接口方法
    public SysUserLoginResp login(SysUserLoginReq req) {
        // 根据登录名查询用户信息
        SysUserEntity userDb = selectByLoginName(req.getLoginName());

        // 第一步：判断用户是否存在
        if (ObjectUtils.isEmpty(userDb)) {
            // 用户不存在，返回null表示登录失败
            return null;
        }

        // 第二步：验证密码是否正确
        // 比较数据库中存储的加密密码与请求中的加密密码是否一致
        if (!userDb.getPassword().equals(req.getPassword())) {
            // 密码错误，返回null表示登录失败
            return null;
        }

        // 第三步：用户存在且密码正确，登录成功
        // 使用工具类将实体对象转换为响应对象
        SysUserLoginResp userLoginResp = CopyUtil.copy(userDb, SysUserLoginResp.class);
        return userLoginResp;
    }

    /**
     * 根据登录名查询用户信息
     * 私有方法，用于检查用户名是否已被注册
     *
     * @param loginName 用户登录名
     * @return SysUserEntity 用户实体对象；如果不存在返回null
     */
    public SysUserEntity selectByLoginName(String loginName) {
        // 创建MyBatis-Plus查询条件构造器
        QueryWrapper<SysUserEntity> wrapper = new QueryWrapper<>();

        // 使用Lambda表达式构建查询条件：loginName字段等于传入的参数
        wrapper.lambda().eq(SysUserEntity::getLoginName, loginName);

        // 执行查询，获取符合条件的用户列表
        List<SysUserEntity> userEntityList = sysUserMapper.selectList(wrapper);

        // 判断查询结果是否为空
        if (CollectionUtils.isEmpty(userEntityList)) {
            // 没有找到用户，返回null
            return null;
        } else {
            // 找到用户，返回第一个结果（理论上登录名唯一，只会有一个结果）
            return userEntityList.get(0);
        }
    }
}
