package top.auunes.demo.service.impl;

// 导入MyBatis-Plus查询条件构造器和分页
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
// 导入Spring相关注解
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

// 导入项目相关类
import top.auunes.demo.entity.ArticleEntity;
import top.auunes.demo.entity.CategoryEntity;
import top.auunes.demo.mapper.ArticleMapper;
import top.auunes.demo.mapper.CategoryMapper;
import top.auunes.demo.req.CategoryCreateReq;
import top.auunes.demo.req.CategoryListReq;
import top.auunes.demo.req.CategoryUpdateReq;
import top.auunes.demo.resp.CategoryResp;
import top.auunes.demo.resp.PageResp;
import top.auunes.demo.service.CategoryService;
import top.auunes.demo.utils.CopyUtil;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 分类服务实现类
 * 实现分类相关的业务逻辑，包括分类的CRUD操作
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Service // Spring服务注解，标识这是一个服务层组件
public class CategoryServiceImpl implements CategoryService {

    /**
     * 分类数据访问层对象
     */
    @Resource
    private CategoryMapper categoryMapper;

    /**
     * 文章数据访问层对象
     * 用于检查分类下是否有文章
     */
    @Resource
    private ArticleMapper articleMapper;

    /**
     * 创建分类方法实现
     * 创建新分类并进行参数验证
     *
     * @param req 分类创建请求对象
     * @return Long 创建成功的分类ID
     */
    @Override
    @Transactional // 事务注解，确保数据一致性
    public Long createCategory(CategoryCreateReq req) {
        // 第一步：参数验证
        validateCreateRequest(req);

        // 第二步：检查分类名称是否已存在
        if (isCategoryNameExists(req.getName(), null)) {
            throw new RuntimeException("分类名称已存在");
        }

        // 第三步：创建分类实体
        CategoryEntity category = new CategoryEntity();
        category.setName(req.getName());
        category.setDescription(req.getDescription());
        category.setSortOrder(req.getSortOrder() != null ? req.getSortOrder() : 0);
        category.setCreateTime(LocalDateTime.now());
        category.setUpdateTime(LocalDateTime.now());

        // 第四步：插入分类
        int result = categoryMapper.insert(category);
        if (result <= 0) {
            throw new RuntimeException("分类创建失败");
        }

        return category.getId();
    }

    /**
     * 获取所有分类列表方法实现
     * 获取所有分类信息，按排序值升序排列（不分页）
     * 对应接口：GET /api/category/list/all
     *
     * @return List<CategoryResp> 分类列表
     */
    @Override
    public List<CategoryResp> getAllCategories() {
        // 创建查询条件，按排序值升序排列
        QueryWrapper<CategoryEntity> wrapper = new QueryWrapper<>();
        wrapper.orderByAsc("sort_order", "create_time");

        // 查询所有分类
        List<CategoryEntity> categoryList = categoryMapper.selectList(wrapper);

        // 转换为响应对象
        return categoryList.stream()
                .map(this::convertToResp)
                .collect(Collectors.toList());
    }

    /**
     * 分页获取分类列表方法实现
     * 根据条件分页查询分类信息
     * 对应接口：POST /api/category/list
     *
     * @param req 分类列表查询请求对象
     * @return PageResp<CategoryResp> 分页分类列表
     */
    @Override
    public PageResp<CategoryResp> getCategoryList(CategoryListReq req) {
        // 第一步：参数验证和默认值设置
        if (req.getPageNum() == null || req.getPageNum() < 1) {
            req.setPageNum(1);
        }
        if (req.getPageSize() == null || req.getPageSize() < 1) {
            req.setPageSize(10);
        }
        if (req.getPageSize() > 100) {
            req.setPageSize(100); // 限制最大页面大小
        }

        // 第二步：创建分页对象
        Page<CategoryEntity> page = new Page<>(req.getPageNum(), req.getPageSize());

        // 第三步：创建查询条件
        QueryWrapper<CategoryEntity> wrapper = new QueryWrapper<>();

        // 如果有名称条件，添加模糊查询
        if (StringUtils.hasText(req.getName())) {
            wrapper.like("name", req.getName());
        }

        // 按排序值和创建时间升序排列
        wrapper.orderByAsc("sort_order", "create_time");

        // 第四步：执行分页查询
        Page<CategoryEntity> categoryPage = categoryMapper.selectPage(page, wrapper);

        // 第五步：转换为响应对象
        List<CategoryResp> categoryRespList = categoryPage.getRecords().stream()
                .map(this::convertToResp)
                .collect(Collectors.toList());

        // 第六步：构建分页响应
        return new PageResp<>(categoryPage.getTotal(), categoryRespList);
    }

    /**
     * 获取分类详情方法实现
     * 根据分类ID获取分类的详细信息
     *
     * @param categoryId 分类ID
     * @return CategoryResp 分类详情响应对象
     */
    @Override
    public CategoryResp getCategoryDetail(Long categoryId) {
        // 第一步：参数验证
        if (categoryId == null) {
            throw new RuntimeException("分类ID不能为空");
        }

        // 第二步：查询分类信息
        CategoryEntity category = categoryMapper.selectById(categoryId);
        if (category == null) {
            throw new RuntimeException("分类不存在");
        }

        // 第三步：转换为响应对象
        return convertToResp(category);
    }

    /**
     * 更新分类方法实现
     * 根据请求参数更新分类信息
     *
     * @param req 分类更新请求对象
     * @return Boolean 更新是否成功
     */
    @Override
    @Transactional
    public Boolean updateCategory(CategoryUpdateReq req) {
        // 第一步：参数验证
        if (req.getId() == null) {
            throw new RuntimeException("分类ID不能为空");
        }

        // 第二步：检查分类是否存在
        CategoryEntity existingCategory = categoryMapper.selectById(req.getId());
        if (existingCategory == null) {
            throw new RuntimeException("分类不存在");
        }

        // 第三步：检查分类名称是否重复（如果要更新名称）
        if (StringUtils.hasText(req.getName()) &&
                !req.getName().equals(existingCategory.getName()) &&
                isCategoryNameExists(req.getName(), req.getId())) {
            throw new RuntimeException("分类名称已存在");
        }

        // 第四步：更新分类信息
        CategoryEntity updateCategory = new CategoryEntity();
        updateCategory.setId(req.getId());
        updateCategory.setUpdateTime(LocalDateTime.now());

        // 只更新非空字段
        if (StringUtils.hasText(req.getName())) {
            if (req.getName().length() > 100) {
                throw new RuntimeException("分类名称不能超过100个字符");
            }
            updateCategory.setName(req.getName());
        }

        if (req.getDescription() != null) {
            updateCategory.setDescription(req.getDescription());
        }

        if (req.getSortOrder() != null) {
            updateCategory.setSortOrder(req.getSortOrder());
        }

        // 第五步：执行更新
        int result = categoryMapper.updateById(updateCategory);
        return result > 0;
    }

    /**
     * 删除分类方法实现
     * 根据分类ID删除分类，检查是否有关联文章
     *
     * @param categoryId 分类ID
     * @return Boolean 删除是否成功
     */
    @Override
    @Transactional
    public Boolean deleteCategory(Long categoryId) {
        // 第一步：参数验证
        if (categoryId == null) {
            throw new RuntimeException("分类ID不能为空");
        }

        // 第二步：检查分类是否存在
        CategoryEntity category = categoryMapper.selectById(categoryId);
        if (category == null) {
            throw new RuntimeException("分类不存在");
        }

        // 第三步：检查分类下是否有文章
        QueryWrapper<ArticleEntity> articleWrapper = new QueryWrapper<>();
        articleWrapper.eq("category_id", categoryId);
        Long articleCount = Long.valueOf(articleMapper.selectCount(articleWrapper));

        if (articleCount > 0) {
            throw new RuntimeException("该分类下还有文章，无法删除");
        }

        // 第四步：删除分类
        int result = categoryMapper.deleteById(categoryId);
        return result > 0;
    }

    /**
     * 检查分类名称是否已存在方法实现
     * 用于创建和更新时的重复性验证
     *
     * @param name 分类名称
     * @param excludeId 排除的分类ID（更新时使用）
     * @return Boolean 名称是否已存在
     */
    @Override
    public Boolean isCategoryNameExists(String name, Long excludeId) {
        if (!StringUtils.hasText(name)) {
            return false;
        }

        QueryWrapper<CategoryEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("name", name);

        // 如果是更新操作，排除当前分类ID
        if (excludeId != null) {
            wrapper.ne("id", excludeId);
        }

        Long count = Long.valueOf(categoryMapper.selectCount(wrapper));
        return count > 0;
    }

    /**
     * 更新分类的文章数量方法实现
     * 当文章的分类发生变化时，更新相关分类的文章统计数量
     *
     * @param categoryId 分类ID
     */
    @Override
    public void updateCategoryArticleCount(Long categoryId) {
        if (categoryId == null) {
            return;
        }

        // 统计该分类下已发布的文章数量
        QueryWrapper<ArticleEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("category_id", categoryId)
               .eq("status", "published");

        Long articleCount = Long.valueOf(articleMapper.selectCount(wrapper));

        // 这里由于数据库表中没有article_count字段，我们不需要更新数据库
        // 文章数量将在convertToResp方法中动态计算
        // 如果需要缓存文章数量，可以考虑添加Redis缓存或修改数据库表结构
    }

    /**
     * 获取热门分类列表方法实现
     * 根据文章数量倒序获取热门分类
     *
     * @param limit 限制数量
     * @return List<CategoryResp> 热门分类列表
     */
    @Override
    public List<CategoryResp> getHotCategories(Integer limit) {
        // 查询所有分类
        QueryWrapper<CategoryEntity> wrapper = new QueryWrapper<>();
        wrapper.orderByAsc("sort_order", "create_time");

        if (limit != null && limit > 0) {
            wrapper.last("LIMIT " + limit);
        }

        List<CategoryEntity> categoryList = categoryMapper.selectList(wrapper);

        // 转换为响应对象并按文章数量排序
        return categoryList.stream()
                .map(this::convertToResp)
                .sorted((c1, c2) -> Integer.compare(c2.getArticleCount(), c1.getArticleCount()))
                .collect(Collectors.toList());
    }

    /**
     * 验证创建请求参数
     */
    private void validateCreateRequest(CategoryCreateReq req) {
        if (!StringUtils.hasText(req.getName())) {
            throw new RuntimeException("分类名称不能为空");
        }
        if (req.getName().length() > 100) {
            throw new RuntimeException("分类名称不能超过100个字符");
        }
        if (req.getDescription() != null && req.getDescription().length() > 500) {
            throw new RuntimeException("分类描述不能超过500个字符");
        }
    }

    /**
     * 将分类实体转换为响应对象
     */
    private CategoryResp convertToResp(CategoryEntity category) {
        CategoryResp resp = new CategoryResp();
        resp.setId(category.getId());
        resp.setName(category.getName());
        resp.setDescription(category.getDescription());
        resp.setSortOrder(category.getSortOrder());
        resp.setCreateTime(category.getCreateTime());
        resp.setUpdateTime(category.getUpdateTime());

        // 动态计算该分类下已发布的文章数量
        QueryWrapper<ArticleEntity> articleWrapper = new QueryWrapper<>();
        articleWrapper.eq("category_id", category.getId())
                     .eq("status", "published");
        Long articleCount = Long.valueOf(articleMapper.selectCount(articleWrapper));
        resp.setArticleCount(articleCount.intValue());

        return resp;
    }
}