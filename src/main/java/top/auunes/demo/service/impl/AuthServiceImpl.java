package top.auunes.demo.service.impl;

// 导入MyBatis-Plus查询条件构造器
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
// 导入Spring相关注解
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

// 导入项目相关类
import top.auunes.demo.entity.AuthUserEntity;
import top.auunes.demo.mapper.AuthUserMapper;
import top.auunes.demo.req.AuthLoginReq;
import top.auunes.demo.resp.AuthLoginResp;
import top.auunes.demo.service.AuthService;
import top.auunes.demo.utils.JwtTokenUtil;

import javax.annotation.Resource;
import java.util.List;

/**
 * 认证服务实现类
 * 实现用户认证相关的业务逻辑，包括用户登录验证和JWT Token生成
 * 独立于原有的用户服务系统
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Service // Spring服务注解，标识这是一个服务层组件
public class AuthServiceImpl implements AuthService {

    /**
     * 认证用户数据访问层对象
     * 使用@Resource注解注入，用于执行数据库操作
     */
    @Resource // Java标准注解，按名称注入依赖
    private AuthUserMapper authUserMapper;

    /**
     * JWT工具类
     * 用于生成和验证JWT Token
     */
    @Autowired // Spring自动装配注解，按类型注入依赖
    private JwtTokenUtil jwtTokenUtil;

    /**
     * 用户登录方法实现
     * 验证用户登录信息并生成JWT Token
     * 
     * @param req 认证登录请求对象，包含用户名和密码
     * @return AuthLoginResp 登录响应对象，包含JWT Token和用户信息
     * @throws RuntimeException 当用户名或密码错误时抛出业务异常
     */
    @Override // 重写接口方法
    public AuthLoginResp login(AuthLoginReq req) {
        // 第一步：参数验证
        if (!StringUtils.hasText(req.getUsername())) {
            throw new RuntimeException("用户名不能为空");
        }
        if (!StringUtils.hasText(req.getPassword())) {
            throw new RuntimeException("密码不能为空");
        }

        // 第二步：对密码进行MD5加密
        String encryptedPassword = DigestUtils.md5DigestAsHex(req.getPassword().getBytes());

        // 第三步：根据用户名查询用户信息
        AuthUserEntity userEntity = selectByUsername(req.getUsername());

        // 第四步：验证用户是否存在
        if (userEntity == null) {
            throw new RuntimeException("用户名或密码错误");
        }

        // 第五步：验证用户状态是否正常
        if (userEntity.getStatus() == null || userEntity.getStatus() != 1) {
            throw new RuntimeException("用户账号已被禁用");
        }

        // 第六步：验证密码是否正确
        if (!userEntity.getPassword().equals(encryptedPassword)) {
            throw new RuntimeException("用户名或密码错误");
        }

        // 第七步：生成JWT Token
        String token = jwtTokenUtil.generateToken(userEntity.getId(), userEntity.getUsername());

        // 第八步：构建登录响应对象
        AuthLoginResp loginResp = new AuthLoginResp();
        loginResp.setToken(token);
        loginResp.setUsername(userEntity.getUsername());
        loginResp.setUserId(userEntity.getId());
        loginResp.setNickname(userEntity.getNickname() != null ? userEntity.getNickname() : userEntity.getUsername());
        loginResp.setAvatar(userEntity.getAvatar() != null ? userEntity.getAvatar() : "");

        return loginResp;
    }

    /**
     * 用户登出方法实现
     * 处理用户登出业务逻辑
     * JWT是无状态的，通常登出由前端处理（删除本地token）
     */
    @Override // 重写接口方法
    public void logout() {
        // JWT是无状态的，服务端不需要特殊处理
        // 如果需要实现token黑名单功能，可以在这里添加相关逻辑
        // 例如：将token添加到Redis黑名单中，设置过期时间等于token的剩余有效期
        
        // 目前只是一个空实现，实际的登出由前端删除本地存储的token完成
    }

    /**
     * 根据用户名查询用户信息
     * 私有方法，用于登录验证时查询用户
     * 
     * @param username 用户名
     * @return AuthUserEntity 用户实体对象，如果不存在返回null
     */
    private AuthUserEntity selectByUsername(String username) {
        // 创建MyBatis-Plus查询条件构造器
        QueryWrapper<AuthUserEntity> wrapper = new QueryWrapper<>();
        
        // 使用Lambda表达式构建查询条件：username字段等于传入的参数
        wrapper.lambda().eq(AuthUserEntity::getUsername, username);
        
        // 执行查询，获取符合条件的用户列表
        List<AuthUserEntity> userList = authUserMapper.selectList(wrapper);
        
        // 判断查询结果是否为空
        if (userList == null || userList.isEmpty()) {
            // 没有找到用户，返回null
            return null;
        } else {
            // 找到用户，返回第一个结果（理论上用户名唯一，只会有一个结果）
            return userList.get(0);
        }
    }
}
