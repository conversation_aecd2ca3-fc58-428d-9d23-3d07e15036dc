package top.auunes.demo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.auunes.demo.entity.ArticleTagEntity;
import top.auunes.demo.entity.TagEntity;
import top.auunes.demo.mapper.ArticleTagMapper;
import top.auunes.demo.mapper.TagMapper;
import top.auunes.demo.req.TagCreateReq;
import top.auunes.demo.req.TagListReq;
import top.auunes.demo.req.TagUpdateReq;
import top.auunes.demo.resp.PageResp;
import top.auunes.demo.resp.TagResp;
import top.auunes.demo.service.TagService;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 标签服务实现类
 * 实现标签相关的业务逻辑，包括标签的CRUD操作
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Service
public class TagServiceImpl implements TagService {
    
    @Autowired
    TagMapper tagMapper;

    @Autowired
    ArticleTagMapper articleTagMapper;
    
    @Override
    public Long createTag(TagCreateReq req) {
        return 0L;
    }


    /**
     * 获取标签列表方法实现
     * 获取所有标签信息，按创建时间升序排列
     * 对应接口：GET /api/tag/list/all
     *
     * @return List<TagResp> 标签列表
     */
    @Override
    public List<TagResp> getAllTags() {
        // 创建查询条件，按创建时间升序排列
        QueryWrapper<TagEntity> wrapper = new QueryWrapper<>();
        wrapper.orderByAsc("create_time");

        // 查询所有标签
        List<TagEntity> tagList = tagMapper.selectList(wrapper);

        // 转换为响应对象
        return tagList.stream()
                .map(this::convertToResp)
                .collect(Collectors.toList());
    }

    @Override
    public PageResp<TagResp> getTagList(TagListReq req) {
        return null;
    }

    @Override
    public Boolean updateTag(TagUpdateReq req) {
        return null;
    }

    @Override
    public TagResp getTagDetail(Long tagId) {
        return null;
    }

    @Override
    public Boolean deleteTag(Long tagId) {
        return null;
    }

    @Override
    public Boolean isTagNameExists(String name, Long excludeId) {
        return null;
    }

    @Override
    public void updateTagArticleCount(Long tagId) {

    }

    @Override
    public List<TagResp> getHotTags(Integer limit) {
        return Collections.emptyList();
    }

    @Override
    public Boolean batchDeleteTags(List<Long> tagIds) {
        return null;
    }

    @Override
    public List<TagResp> getTagsByArticleId(Long articleId) {
        return Collections.emptyList();
    }

    @Override
    public void setArticleTags(Long articleId, List<Long> tagIds) {

    }

    /**
     * 将标签实体转换为响应对象
     * 包含动态计算文章数量
     */
    private TagResp convertToResp(TagEntity tag) {
        TagResp resp = new TagResp();
        resp.setId(tag.getId());
        resp.setName(tag.getName());
        resp.setColor(tag.getColor());
        resp.setCreateTime(tag.getCreateTime());
        resp.setUpdateTime(tag.getUpdateTime());

        // 动态计算该标签下已发布的文章数量
        QueryWrapper<ArticleTagEntity> articleTagWrapper = new QueryWrapper<>();
        articleTagWrapper.eq("tag_id", tag.getId());
        Long articleCount = Long.valueOf(articleTagMapper.selectCount(articleTagWrapper));
        resp.setArticleCount(articleCount.intValue());

        return resp;
    }
}
