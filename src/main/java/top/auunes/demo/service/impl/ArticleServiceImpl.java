package top.auunes.demo.service.impl;

// 导入MyBatis-Plus查询条件构造器

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import top.auunes.demo.entity.*;
import top.auunes.demo.mapper.*;
import top.auunes.demo.req.ArticleCreateReq;
import top.auunes.demo.req.ArticleUpdateReq;
import top.auunes.demo.resp.ArticleDetailResp;
import top.auunes.demo.service.ArticleService;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 文章服务实现类
 * 实现文章相关的业务逻辑，包括文章的CRUD操作和标签关联管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Service // Spring服务注解，标识这是一个服务层组件
public class ArticleServiceImpl implements ArticleService {

    /**
     * 文章数据访问层对象
     */
    @Resource
    private ArticleMapper articleMapper;

    /**
     * 分类数据访问层对象
     */
    @Resource
    private CategoryMapper categoryMapper;

    /**
     * 标签数据访问层对象
     */
    @Resource
    private TagMapper tagMapper;

    /**
     * 文章标签关联数据访问层对象
     */
    @Resource
    private ArticleTagMapper articleTagMapper;

    /**
     * 认证用户数据访问层对象
     */
    @Resource
    private AuthUserMapper authUserMapper;

    /**
     * 创建文章方法实现
     * 创建新文章并处理标签关联关系
     *
     * @param req      文章创建请求对象
     * @param authorId 作者ID
     * @return Long 创建成功的文章ID
     */
    @Override
    @Transactional // 事务注解，确保数据一致性
    public Long createArticle(ArticleCreateReq req, Long authorId) {
        // 第一步：参数验证
        validateCreateRequest(req);

        // 第二步：验证分类是否存在
        if (req.getCategoryId() != null) {
            CategoryEntity category = categoryMapper.selectById(req.getCategoryId());
            if (category == null) {
                throw new RuntimeException("分类不存在");
            }
        }

        // 第三步：创建文章实体
        ArticleEntity article = new ArticleEntity();
        article.setTitle(req.getTitle());
        article.setContent(req.getContent());
        article.setSummary(generateSummary(req.getSummary(), req.getContent()));
        article.setCoverImage(req.getCoverImage());
        article.setStatus(StringUtils.hasText(req.getStatus()) ? req.getStatus() : "draft");
        article.setCategoryId(req.getCategoryId());
        article.setAuthorId(authorId);
        article.setViewCount(0);
        article.setLikeCount(0);
        article.setCommentCount(0);
        article.setIsTop(req.getIsTop() != null ? req.getIsTop() : 0);
        article.setIsRecommend(req.getIsRecommend() != null ? req.getIsRecommend() : 0);
        article.setCreateTime(LocalDateTime.now());
        article.setUpdateTime(LocalDateTime.now());

        // 如果是发布状态，设置发布时间
        if ("published".equals(article.getStatus())) {
            article.setPublishTime(LocalDateTime.now());
        }

        // 第四步：插入文章
        int result = articleMapper.insert(article);
        if (result <= 0) {
            throw new RuntimeException("文章创建失败");
        }

        // 第五步：处理标签关联
        if (req.getTagIds() != null && !req.getTagIds().isEmpty()) {
            createArticleTagRelations(article.getId(), req.getTagIds());
        }

        return article.getId();
    }

    /**
     * 获取文章详情方法实现
     * 获取文章详细信息并增加阅读次数
     *
     * @param articleId 文章ID
     * @return ArticleDetailResp 文章详情响应对象
     */
    @Override
    @Transactional
    public ArticleDetailResp getArticleDetail(Long articleId) {
        // 第一步：参数验证
        if (articleId == null) {
            throw new RuntimeException("文章ID不能为空");
        }

        // 第二步：查询文章基本信息
        ArticleEntity article = articleMapper.selectById(articleId);
        if (article == null) {
            throw new RuntimeException("文章不存在");
        }

        // 第三步：增加阅读次数
        incrementViewCount(articleId);

        // 第四步：构建响应对象
        ArticleDetailResp resp = new ArticleDetailResp();

        // 复制基本信息
        resp.setId(article.getId());
        resp.setTitle(article.getTitle());
        resp.setContent(article.getContent());
        resp.setSummary(article.getSummary());
        resp.setCoverImage(article.getCoverImage());
        resp.setStatus(article.getStatus());
        resp.setViewCount(article.getViewCount() + 1); // 显示增加后的阅读次数
        resp.setLikeCount(article.getLikeCount());
        resp.setCommentCount(article.getCommentCount());
        resp.setIsTop(article.getIsTop());
        resp.setIsRecommend(article.getIsRecommend());
        resp.setCreateTime(article.getCreateTime());
        resp.setUpdateTime(article.getUpdateTime());
        resp.setPublishTime(article.getPublishTime());

        // 第五步：设置分类信息
        if (article.getCategoryId() != null) {
            CategoryEntity category = categoryMapper.selectById(article.getCategoryId());
            if (category != null) {
                resp.setCategory(new ArticleDetailResp.CategoryInfo(
                        category.getId(), category.getName()));
            }
        }

        // 第六步：设置标签信息
        resp.setTags(getArticleTags(articleId));

        // 第七步：设置作者信息
        if (article.getAuthorId() != null) {
            AuthUserEntity author = authUserMapper.selectById(article.getAuthorId());
            if (author != null) {
                resp.setAuthor(new ArticleDetailResp.AuthorInfo(
                        author.getId(), author.getUsername(), author.getNickname(), author.getAvatar()));
            }
        }

        return resp;
    }

    /**
     * 验证创建请求参数
     */
    private void validateCreateRequest(ArticleCreateReq req) {
        if (!StringUtils.hasText(req.getTitle())) {
            throw new RuntimeException("文章标题不能为空");
        }
        if (!StringUtils.hasText(req.getContent())) {
            throw new RuntimeException("文章内容不能为空");
        }
        if (req.getTitle().length() > 200) {
            throw new RuntimeException("文章标题不能超过200个字符");
        }
    }

    /**
     * 生成文章摘要
     */
    private String generateSummary(String summary, String content) {
        if (StringUtils.hasText(summary)) {
            return summary.length() > 500 ? summary.substring(0, 500) + "..." : summary;
        }

        if (StringUtils.hasText(content)) {
            // 简单的摘要生成：去除HTML标签，截取前200个字符
            String plainText = content.replaceAll("<[^>]*>", "").replaceAll("\\s+", " ").trim();
            return plainText.length() > 200 ? plainText.substring(0, 200) + "..." : plainText;
        }

        return "";
    }

    /**
     * 创建文章标签关联关系
     */
    private void createArticleTagRelations(Long articleId, List<Long> tagIds) {
        // 验证标签是否存在
        for (Long tagId : tagIds) {
            TagEntity tag = tagMapper.selectById(tagId);
            if (tag == null) {
                throw new RuntimeException("标签ID " + tagId + " 不存在");
            }
        }

        // 创建关联关系
        for (Long tagId : tagIds) {
            ArticleTagEntity articleTag = new ArticleTagEntity(articleId, tagId);
            articleTagMapper.insert(articleTag);
        }
    }

    /**
     * 增加文章阅读次数
     */
    private void incrementViewCount(Long articleId) {
        UpdateWrapper<ArticleEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", articleId)
                .setSql("view_count = view_count + 1");
        articleMapper.update(null, updateWrapper);
    }

    /**
     * 获取文章的标签列表
     */
    private List<ArticleDetailResp.TagInfo> getArticleTags(Long articleId) {
        // 查询文章标签关联关系
        QueryWrapper<ArticleTagEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("article_id", articleId);
        List<ArticleTagEntity> articleTags = articleTagMapper.selectList(wrapper);

        if (articleTags.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取标签ID列表
        List<Long> tagIds = articleTags.stream()
                .map(ArticleTagEntity::getTagId)
                .collect(Collectors.toList());

        // 查询标签信息
        List<TagEntity> tags = tagMapper.selectBatchIds(tagIds);

        // 转换为响应对象
        return tags.stream()
                .map(tag -> new ArticleDetailResp.TagInfo(tag.getId(), tag.getName()))
                .collect(Collectors.toList());
    }


    /**
     * 更新文章方法实现
     * 更新文章信息并处理标签关联关系的变更
     *
     * @param req      文章更新请求对象
     * @param authorId 作者ID
     * @return Boolean 更新是否成功
     */
    @Override
    @Transactional
    public Boolean updateArticle(ArticleUpdateReq req, Long authorId) {
        // 第一步：参数验证
        if (req.getId() == null) {
            throw new RuntimeException("文章ID不能为空");
        }

        // 第二步：检查文章是否存在和权限
        ArticleEntity existingArticle = articleMapper.selectById(req.getId());
        if (existingArticle == null) {
            throw new RuntimeException("文章不存在");
        }

        if (!existingArticle.getAuthorId().equals(authorId)) {
            throw new RuntimeException("无权限修改此文章");
        }

        // 第三步：验证分类是否存在
        if (req.getCategoryId() != null) {
            CategoryEntity category = categoryMapper.selectById(req.getCategoryId());
            if (category == null) {
                throw new RuntimeException("分类不存在");
            }
        }

        // 第四步：更新文章信息
        ArticleEntity updateArticle = new ArticleEntity();
        updateArticle.setId(req.getId());
        updateArticle.setUpdateTime(LocalDateTime.now());

        // 只更新非空字段
        if (StringUtils.hasText(req.getTitle())) {
            if (req.getTitle().length() > 200) {
                throw new RuntimeException("文章标题不能超过200个字符");
            }
            updateArticle.setTitle(req.getTitle());
        }

        if (StringUtils.hasText(req.getContent())) {
            updateArticle.setContent(req.getContent());
            // 如果内容更新了，重新生成摘要
            updateArticle.setSummary(generateSummary(req.getSummary(), req.getContent()));
        } else if (StringUtils.hasText(req.getSummary())) {
            updateArticle.setSummary(req.getSummary());
        }

        if (StringUtils.hasText(req.getCoverImage())) {
            updateArticle.setCoverImage(req.getCoverImage());
        }

        if (StringUtils.hasText(req.getStatus())) {
            updateArticle.setStatus(req.getStatus());
            // 如果状态改为发布，设置发布时间
            if ("published".equals(req.getStatus()) && !"published".equals(existingArticle.getStatus())) {
                updateArticle.setPublishTime(LocalDateTime.now());
            }
        }

        if (req.getCategoryId() != null) {
            updateArticle.setCategoryId(req.getCategoryId());
        }

        if (req.getIsTop() != null) {
            updateArticle.setIsTop(req.getIsTop());
        }

        if (req.getIsRecommend() != null) {
            updateArticle.setIsRecommend(req.getIsRecommend());
        }

        // 第五步：执行更新
        int result = articleMapper.updateById(updateArticle);
        if (result <= 0) {
            throw new RuntimeException("文章更新失败");
        }

        // 第六步：处理标签关联关系的变更
        if (req.getTagIds() != null) {
            updateArticleTagRelations(req.getId(), req.getTagIds());
        }

        return true;
    }

    /**
     * 删除文章方法实现
     * 删除文章及其相关的标签关联关系
     *
     * @param articleId 文章ID
     * @param authorId  作者ID
     * @return Boolean 删除是否成功
     */
    @Override
    @Transactional
    public Boolean deleteArticle(Long articleId, Long authorId) {
        // 第一步：参数验证
        if (articleId == null) {
            throw new RuntimeException("文章ID不能为空");
        }

        // 第二步：检查文章是否存在和权限
        ArticleEntity article = articleMapper.selectById(articleId);
        if (article == null) {
            throw new RuntimeException("文章不存在");
        }

        if (!article.getAuthorId().equals(authorId)) {
            throw new RuntimeException("无权限删除此文章");
        }

        // 第三步：删除文章标签关联关系
        QueryWrapper<ArticleTagEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("article_id", articleId);
        articleTagMapper.delete(wrapper);

        // 第四步：删除文章
        int result = articleMapper.deleteById(articleId);
        if (result <= 0) {
            throw new RuntimeException("文章删除失败");
        }

        return true;
    }

    /**
     * 发布文章方法实现
     * 将草稿状态的文章发布
     *
     * @param articleId 文章ID
     * @param authorId  作者ID
     * @return Boolean 发布是否成功
     */
    @Override
    public Boolean publishArticle(Long articleId, Long authorId) {
        return updateArticleStatus(articleId, authorId, "published");
    }

    /**
     * 取消发布文章方法实现
     * 将已发布状态的文章改为草稿
     *
     * @param articleId 文章ID
     * @param authorId  作者ID
     * @return Boolean 取消发布是否成功
     */
    @Override
    public Boolean unpublishArticle(Long articleId, Long authorId) {
        return updateArticleStatus(articleId, authorId, "draft");
    }

    /**
     * 点赞文章方法实现
     * 增加文章的点赞次数
     *
     * @param articleId 文章ID
     * @return Boolean 点赞是否成功
     */
    @Override
    public Boolean likeArticle(Long articleId) {
        // 第一步：参数验证
        if (articleId == null) {
            throw new RuntimeException("文章ID不能为空");
        }

        // 第二步：检查文章是否存在
        ArticleEntity article = articleMapper.selectById(articleId);
        if (article == null) {
            throw new RuntimeException("文章不存在");
        }

        // 第三步：增加点赞次数
        UpdateWrapper<ArticleEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", articleId)
                .setSql("like_count = like_count + 1");
        int result = articleMapper.update(null, updateWrapper);

        return result > 0;
    }

    /**
     * 检查文章权限方法实现
     * 验证当前用户是否有权限操作指定文章
     *
     * @param articleId 文章ID
     * @param authorId  作者ID
     * @return Boolean 是否有权限
     */
    @Override
    public Boolean checkArticlePermission(Long articleId, Long authorId) {
        if (articleId == null || authorId == null) {
            return false;
        }

        ArticleEntity article = articleMapper.selectById(articleId);
        if (article == null) {
            return false;
        }

        return article.getAuthorId().equals(authorId);
    }

    /**
     * 更新文章状态的通用方法
     */
    private Boolean updateArticleStatus(Long articleId, Long authorId, String status) {
        // 第一步：参数验证
        if (articleId == null) {
            throw new RuntimeException("文章ID不能为空");
        }

        // 第二步：检查文章是否存在和权限
        ArticleEntity article = articleMapper.selectById(articleId);
        if (article == null) {
            throw new RuntimeException("文章不存在");
        }

        if (!article.getAuthorId().equals(authorId)) {
            throw new RuntimeException("无权限操作此文章");
        }

        // 第三步：更新状态
        ArticleEntity updateArticle = new ArticleEntity();
        updateArticle.setId(articleId);
        updateArticle.setStatus(status);
        updateArticle.setUpdateTime(LocalDateTime.now());

        // 如果是发布操作，设置发布时间
        if ("published".equals(status)) {
            updateArticle.setPublishTime(LocalDateTime.now());
        }

        int result = articleMapper.updateById(updateArticle);
        return result > 0;
    }

    /**
     * 更新文章标签关联关系
     */
    private void updateArticleTagRelations(Long articleId, List<Long> tagIds) {
        // 第一步：删除原有的关联关系
        QueryWrapper<ArticleTagEntity> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.eq("article_id", articleId);
        articleTagMapper.delete(deleteWrapper);

        // 第二步：创建新的关联关系
        if (tagIds != null && !tagIds.isEmpty()) {
            createArticleTagRelations(articleId, tagIds);
        }
    }
}