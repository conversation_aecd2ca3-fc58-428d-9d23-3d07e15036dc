package top.auunes.demo.req;

/**
 * 标签创建请求对象
 * 根据接口文档定义的创建标签请求格式
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public class TagCreateReq {

    /**
     * 标签名称
     * 必填字段，标签的显示名称
     */
    private String name;

    /**
     * 标签颜色
     * 可选字段，用于前端显示标签的颜色，默认为 #007bff
     */
    private String color;

    /**
     * 默认构造函数
     */
    public TagCreateReq() {
    }

    /**
     * 获取标签名称
     *
     * @return String 标签名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置标签名称
     *
     * @param name 标签名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取标签颜色
     *
     * @return String 标签颜色
     */
    public String getColor() {
        return color;
    }

    /**
     * 设置标签颜色
     *
     * @param color 标签颜色
     */
    public void setColor(String color) {
        this.color = color;
    }

    /**
     * 重写toString方法
     * 用于对象的字符串表示，便于调试和日志输出
     *
     * @return String 对象的字符串表示
     */
    @Override
    public String toString() {
        return "TagCreateReq{" +
                "name='" + name + '\'' +
                ", color='" + color + '\'' +
                '}';
    }
}
