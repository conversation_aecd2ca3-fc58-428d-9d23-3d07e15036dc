package top.auunes.demo.req;

/**
 * 系统用户保存请求对象
 * 用于接收前端传递的用户注册或更新信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public class SysUserSaveReq {

    /**
     * 用户ID
     * 新用户注册时为空，更新用户时需要传递用户ID
     */
    private Long id;

    /**
     * 用户登录名
     * 用户注册时输入的登录名，必须唯一
     */
    private String LoginName;

    /**
     * 用户真实姓名
     * 用户的显示名称
     */
    private String name;

    /**
     * 用户密码
     * 用户注册时输入的密码，在控制器中会被MD5加密
     */
    private String password;

    /**
     * 获取用户ID
     *
     * @return Long 用户ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置用户ID
     *
     * @param id 用户ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取用户登录名
     *
     * @return String 用户登录名
     */
    public String getLoginName() {
        return LoginName;
    }

    /**
     * 设置用户登录名
     *
     * @param loginName 用户登录名
     */
    public void setLoginName(String loginName) {
        LoginName = loginName;
    }

    /**
     * 获取用户真实姓名
     *
     * @return String 用户真实姓名
     */
    public String getName() {
        return name;
    }

    /**
     * 设置用户真实姓名
     *
     * @param name 用户真实姓名
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取用户密码
     *
     * @return String 用户密码
     */
    public String getPassword() {
        return password;
    }

    /**
     * 设置用户密码
     *
     * @param password 用户密码
     */
    public void setPassword(String password) {
        this.password = password;
    }

    /**
     * 重写toString方法
     * 用于对象的字符串表示，便于调试和日志输出
     * 注意：为了安全考虑，实际项目中不应该在日志中输出密码信息
     *
     * @return String 对象的字符串表示
     */
    @Override
    public String toString() {
        return "SysUserSaveReq{" +
                "id=" + id +
                ", LoginName='" + LoginName + '\'' +
                ", name='" + name + '\'' +
                ", password='" + password + '\'' +
                '}';
    }
}
