package top.auunes.demo.req;

/**
 * 分类列表查询请求对象
 * 根据接口文档定义的分页查询分类请求格式
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public class CategoryListReq {

    /**
     * 页码
     * 从1开始，默认为1
     */
    private Integer pageNum = 1;

    /**
     * 每页大小
     * 默认为10
     */
    private Integer pageSize = 10;

    /**
     * 分类名称
     * 可选字段，用于模糊查询
     */
    private String name;

    /**
     * 默认构造函数
     */
    public CategoryListReq() {
    }

    /**
     * 获取页码
     *
     * @return Integer 页码
     */
    public Integer getPageNum() {
        return pageNum;
    }

    /**
     * 设置页码
     *
     * @param pageNum 页码
     */
    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    /**
     * 获取每页大小
     *
     * @return Integer 每页大小
     */
    public Integer getPageSize() {
        return pageSize;
    }

    /**
     * 设置每页大小
     *
     * @param pageSize 每页大小
     */
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 获取分类名称
     *
     * @return String 分类名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置分类名称
     *
     * @param name 分类名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 重写toString方法
     * 用于对象的字符串表示，便于调试和日志输出
     *
     * @return String 对象的字符串表示
     */
    @Override
    public String toString() {
        return "CategoryListReq{" +
                "pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                ", name='" + name + '\'' +
                '}';
    }
}
