package top.auunes.demo.req;

/**
 * 标签更新请求对象
 * 根据接口文档定义的更新标签请求格式
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public class TagUpdateReq {

    /**
     * 标签ID
     * 必填字段，用于标识要更新的标签
     */
    private Long id;

    /**
     * 标签名称
     * 可选字段，如果不提供则不更新
     */
    private String name;

    /**
     * 标签颜色
     * 可选字段，用于前端显示标签的颜色
     */
    private String color;

    /**
     * 默认构造函数
     */
    public TagUpdateReq() {
    }

    /**
     * 获取标签ID
     *
     * @return Long 标签ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置标签ID
     *
     * @param id 标签ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取标签名称
     *
     * @return String 标签名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置标签名称
     *
     * @param name 标签名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取标签颜色
     *
     * @return String 标签颜色
     */
    public String getColor() {
        return color;
    }

    /**
     * 设置标签颜色
     *
     * @param color 标签颜色
     */
    public void setColor(String color) {
        this.color = color;
    }

    /**
     * 重写toString方法
     * 用于对象的字符串表示，便于调试和日志输出
     *
     * @return String 对象的字符串表示
     */
    @Override
    public String toString() {
        return "TagUpdateReq{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", color='" + color + '\'' +
                '}';
    }
}
