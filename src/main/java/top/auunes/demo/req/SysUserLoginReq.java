package top.auunes.demo.req;

/**
 * 系统用户登录请求对象
 * 用于接收前端传递的用户登录信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public class SysUserLoginReq {

    /**
     * 用户登录名
     * 用户登录时输入的用户名，用于身份识别
     */
    private String LoginName;

    /**
     * 用户密码
     * 用户登录时输入的密码，在控制器中会被MD5加密
     */
    private String password;

    /**
     * 获取用户登录名
     *
     * @return String 用户登录名
     */
    public String getLoginName() {
        return LoginName;
    }

    /**
     * 设置用户登录名
     *
     * @param loginName 用户登录名
     */
    public void setLoginName(String loginName) {
        LoginName = loginName;
    }

    /**
     * 获取用户密码
     *
     * @return String 用户密码
     */
    public String getPassword() {
        return password;
    }

    /**
     * 设置用户密码
     *
     * @param password 用户密码
     */
    public void setPassword(String password) {
        this.password = password;
    }

    /**
     * 重写toString方法
     * 用于对象的字符串表示，便于调试和日志输出
     * 注意：为了安全考虑，实际项目中不应该在日志中输出密码信息
     *
     * @return String 对象的字符串表示
     */
    @Override
    public String toString() {
        return "SysUserLoginReq{" +
                "LoginName='" + LoginName + '\'' +
                ", password='" + password + '\'' +
                '}';
    }
}
