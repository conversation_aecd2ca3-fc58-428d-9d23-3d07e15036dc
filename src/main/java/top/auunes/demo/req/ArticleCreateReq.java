package top.auunes.demo.req;

import java.util.List;

/**
 * 文章创建请求对象
 * 根据接口文档定义的创建文章请求格式
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public class ArticleCreateReq {

    /**
     * 文章标题
     * 必填字段
     */
    private String title;

    /**
     * 文章内容
     * 必填字段，支持Markdown格式
     */
    private String content;

    /**
     * 文章摘要
     * 可选字段，如果不提供则自动从内容中截取
     */
    private String summary;

    /**
     * 文章封面图片URL
     * 可选字段
     */
    private String coverImage;

    /**
     * 文章状态
     * draft-草稿，published-已发布
     * 默认为draft
     */
    private String status;

    /**
     * 分类ID
     * 必填字段
     */
    private Long categoryId;

    /**
     * 标签ID列表
     * 可选字段，文章可以关联多个标签
     */
    private List<Long> tagIds;

    /**
     * 是否置顶
     * 1-置顶，0-不置顶，默认为0
     */
    private Integer isTop;

    /**
     * 是否推荐
     * 1-推荐，0-不推荐，默认为0
     */
    private Integer isRecommend;

    /**
     * 默认构造函数
     */
    public ArticleCreateReq() {
    }

    // Getter和Setter方法

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getCoverImage() {
        return coverImage;
    }

    public void setCoverImage(String coverImage) {
        this.coverImage = coverImage;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public List<Long> getTagIds() {
        return tagIds;
    }

    public void setTagIds(List<Long> tagIds) {
        this.tagIds = tagIds;
    }

    public Integer getIsTop() {
        return isTop;
    }

    public void setIsTop(Integer isTop) {
        this.isTop = isTop;
    }

    public Integer getIsRecommend() {
        return isRecommend;
    }

    public void setIsRecommend(Integer isRecommend) {
        this.isRecommend = isRecommend;
    }

    @Override
    public String toString() {
        return "ArticleCreateReq{" +
                "title='" + title + '\'' +
                ", summary='" + summary + '\'' +
                ", coverImage='" + coverImage + '\'' +
                ", status='" + status + '\'' +
                ", categoryId=" + categoryId +
                ", tagIds=" + tagIds +
                ", isTop=" + isTop +
                ", isRecommend=" + isRecommend +
                '}';
    }
}