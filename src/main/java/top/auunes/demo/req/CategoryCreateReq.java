package top.auunes.demo.req;

/**
 * 分类创建请求对象
 * 根据接口文档定义的创建分类请求格式
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public class CategoryCreateReq {

    /**
     * 分类名称
     * 必填字段，分类的显示名称
     */
    private String name;

    /**
     * 分类描述
     * 可选字段，分类的详细描述
     */
    private String description;

    /**
     * 排序值
     * 可选字段，数值越小排序越靠前，默认为0
     */
    private Integer sortOrder;

    /**
     * 默认构造函数
     */
    public CategoryCreateReq() {
    }

    /**
     * 获取分类名称
     *
     * @return String 分类名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置分类名称
     *
     * @param name 分类名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取分类描述
     *
     * @return String 分类描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 设置分类描述
     *
     * @param description 分类描述
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 获取排序值
     *
     * @return Integer 排序值
     */
    public Integer getSortOrder() {
        return sortOrder;
    }

    /**
     * 设置排序值
     *
     * @param sortOrder 排序值
     */
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    /**
     * 重写toString方法
     * 用于对象的字符串表示，便于调试和日志输出
     *
     * @return String 对象的字符串表示
     */
    @Override
    public String toString() {
        return "CategoryCreateReq{" +
                "name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", sortOrder=" + sortOrder +
                '}';
    }
}