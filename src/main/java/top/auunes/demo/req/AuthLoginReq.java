package top.auunes.demo.req;

/**
 * 认证登录请求对象
 * 根据接口文档定义的登录请求格式
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public class AuthLoginReq {

    /**
     * 用户名
     * 用户登录时输入的用户名
     */
    private String username;

    /**
     * 密码
     * 用户登录时输入的密码（明文，将在控制器中加密）
     */
    private String password;

    /**
     * 默认构造函数
     */
    public AuthLoginReq() {
    }

    /**
     * 完整构造函数
     * 
     * @param username 用户名
     * @param password 密码
     */
    public AuthLoginReq(String username, String password) {
        this.username = username;
        this.password = password;
    }

    /**
     * 获取用户名
     * 
     * @return String 用户名
     */
    public String getUsername() {
        return username;
    }

    /**
     * 设置用户名
     * 
     * @param username 用户名
     */
    public void setUsername(String username) {
        this.username = username;
    }

    /**
     * 获取密码
     * 
     * @return String 密码
     */
    public String getPassword() {
        return password;
    }

    /**
     * 设置密码
     * 
     * @param password 密码
     */
    public void setPassword(String password) {
        this.password = password;
    }

    /**
     * 重写toString方法
     * 用于对象的字符串表示，便于调试和日志输出
     * 注意：为了安全考虑，不输出密码信息
     * 
     * @return String 对象的字符串表示
     */
    @Override
    public String toString() {
        return "AuthLoginReq{" +
                "username='" + username + '\'' +
                ", password='[HIDDEN]'" +
                '}';
    }
}
