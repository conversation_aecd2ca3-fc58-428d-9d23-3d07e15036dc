package top.auunes.demo.controller;

// 导入Spring相关注解
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

// 导入项目相关类
import top.auunes.demo.req.AuthLoginReq;
import top.auunes.demo.resp.ApiResponse;
import top.auunes.demo.resp.AuthLoginResp;
import top.auunes.demo.service.AuthService;

/**
 * 认证控制器
 * 处理用户认证相关的HTTP请求，符合接口文档规范
 * 使用JWT进行用户认证，不影响原有的登录功能
 * 遵循分层架构原则，只负责HTTP请求处理，业务逻辑在Service层
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@RestController // 标识这是一个REST控制器，返回JSON格式数据
@RequestMapping("/api/auth") // 定义控制器的基础路径为 /api/auth
public class AuthController {

    /**
     * 认证服务对象
     * 通过Spring自动装配注入，处理认证相关业务逻辑
     */
    @Autowired // Spring自动装配注解，自动注入AuthService实例
    private AuthService authService;

    /**
     * 用户登录接口
     * 处理用户登录请求，完整路径为 POST /api/auth/login
     * 根据接口文档规范实现，使用JWT认证
     * 遵循分层架构，业务逻辑在AuthService中处理
     *
     * @param req 认证登录请求对象，包含用户名和密码
     * @return ApiResponse<AuthLoginResp> API统一响应格式，包含JWT token和用户信息
     */
    @PostMapping("/login") // 映射POST请求到 /api/auth/login
    public ApiResponse<AuthLoginResp> login(@RequestBody AuthLoginReq req) {
        try {
            // 调用认证服务处理登录业务逻辑
            AuthLoginResp loginResp = authService.login(req);

            // 返回成功响应
            return ApiResponse.success(loginResp);

        } catch (RuntimeException e) {
            // 业务异常处理：返回业务错误信息
            return ApiResponse.businessError(e.getMessage());

        } catch (Exception e) {
            // 系统异常处理：记录日志并返回系统错误
            // 实际项目中应该使用日志框架记录详细错误信息
            e.printStackTrace();
            return ApiResponse.systemError("系统异常，请稍后重试");
        }
    }

    /**
     * 用户登出接口
     * 处理用户登出请求，完整路径为 POST /api/auth/logout
     * 根据接口文档规范实现
     *
     * @return ApiResponse<Object> API统一响应格式
     */
    @PostMapping("/logout")
    public ApiResponse<Object> logout() {
        try {
            // 调用认证服务处理登出业务逻辑
            authService.logout();

            // 返回成功响应
            return ApiResponse.success();

        } catch (Exception e) {
            // 异常处理：记录日志并返回系统错误
            e.printStackTrace();
            return ApiResponse.systemError("系统异常，请稍后重试");
        }
    }
}
