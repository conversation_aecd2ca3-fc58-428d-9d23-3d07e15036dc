package top.auunes.demo.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.auunes.demo.resp.ApiResponse;
import top.auunes.demo.resp.TagResp;
import top.auunes.demo.service.TagService;

import java.util.List;

/**
 * 标签控制器
 * 处理标签相关的HTTP请求，符合接口文档规范
 * 实现标签管理的全部功能
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/api/tag")
public class TagController {

    @Autowired
    TagService tagService;

    /**
     * 获取标签列表接口
     * 处理获取标签列表请求，完整路径为 GET /api/tag/list/all
     *
     * @return ApiResponse<List < TagResp>> API统一响应格式，包含标签列表
     */
    @GetMapping("/list/all") // 映射GET请求到 /api/tag/list/all
    public ApiResponse<List<TagResp>> getTagList() {
        try {
            // 调用标签服务获取标签列表
            List<TagResp> tagList = tagService.getAllTags();

            // 返回成功响应
            return ApiResponse.success(tagList);

        } catch (RuntimeException e) {
            // 业务异常处理：返回业务错误信息
            return ApiResponse.businessError(e.getMessage());

        } catch (Exception e) {
            // 系统异常处理：记录日志并返回系统错误
            e.printStackTrace();
            return ApiResponse.systemError("系统异常，请稍后重试");
        }
    }
}
