package top.auunes.demo.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.auunes.demo.req.SysUserLoginReq;
import top.auunes.demo.req.SysUserSaveReq;
import top.auunes.demo.resp.CommonResp;
import top.auunes.demo.resp.SysUserLoginResp;
import top.auunes.demo.service.SysUserService;

/**
 * 系统用户控制器
 * 处理用户相关的HTTP请求，包括用户注册和登录
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@RestController // 标识这是一个REST控制器，返回JSON格式数据
@RequestMapping("/sys-user") // 定义控制器的基础路径为 /sys-user
public class SysUserController {

    /**
     * 用户服务对象
     * 通过Spring自动装配注入，处理用户相关业务逻辑
     */
    @Autowired // Spring自动装配注解，自动注入SysUserService实例
    private SysUserService sysUserService;

    /**
     * 用户注册接口
     * 处理用户注册请求，完整路径为 POST /sys-user/register
     *
     * @param req 用户注册请求对象，包含用户注册信息
     * @return CommonResp 通用响应对象，包含操作结果
     */
    @PostMapping("register") // 映射POST请求到 /sys-user/register
    public CommonResp register(@RequestBody SysUserSaveReq req) { // @RequestBody将JSON请求体转换为Java对象
        // 对用户密码进行MD5加密处理
        // DigestUtils.md5DigestAsHex()将字符串转换为MD5哈希值的十六进制表示
        req.setPassword(DigestUtils.md5DigestAsHex(req.getPassword().getBytes()));

        // 创建通用响应对象，默认success为true
        CommonResp resp = new CommonResp<>();

        // 调用用户服务的注册方法，执行注册业务逻辑
        sysUserService.register(req);

        // 返回响应结果给前端
        return resp;
    }

    /**
     * 用户登录接口
     * 处理用户登录请求，完整路径为 POST /sys-user/login
     * 包含完整的登录结果判断和错误处理
     *
     * @param req 用户登录请求对象，包含登录名和密码
     * @return CommonResp 通用响应对象，包含登录结果和用户信息
     */
    @PostMapping("login") // 映射POST请求到 /sys-user/login
    public CommonResp login(@RequestBody SysUserLoginReq req) { // @RequestBody将JSON请求体转换为Java对象
        // 对用户密码进行MD5加密处理，与数据库中存储的加密密码进行比较
        req.setPassword(DigestUtils.md5DigestAsHex(req.getPassword().getBytes()));

        // 创建通用响应对象
        CommonResp resp = new CommonResp<>();

        // 调用用户服务的登录方法，验证用户信息并获取登录结果
        SysUserLoginResp loginResp = sysUserService.login(req);

        // 判断登录结果并设置相应的响应信息
        if (loginResp == null) {
            // 登录失败：用户不存在或密码错误
            resp.setSuccess(false);
            resp.setMessage("用户名或密码错误");
            resp.setContent(null);
        } else {
            // 登录成功：用户存在且密码正确
            resp.setSuccess(true);
            resp.setMessage("登录成功");
            resp.setContent(loginResp);
        }

        // 返回响应结果给前端
        return resp;
    }

}
