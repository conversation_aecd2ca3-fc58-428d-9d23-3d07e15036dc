package top.auunes.demo.controller;

// 导入Spring相关注解
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

// 导入项目相关类
import top.auunes.demo.req.ArticleCreateReq;
import top.auunes.demo.req.ArticleUpdateReq;
import top.auunes.demo.resp.ApiResponse;
import top.auunes.demo.resp.ArticleDetailResp;
import top.auunes.demo.service.ArticleService;
import top.auunes.demo.utils.JwtTokenUtil;

import javax.servlet.http.HttpServletRequest;

/**
 * 文章控制器
 * 处理文章相关的HTTP请求，符合接口文档规范
 * 实现博客文章管理的全部功能
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@RestController // 标识这是一个REST控制器，返回JSON格式数据
@RequestMapping("/api/article") // 定义控制器的基础路径为 /api/article
public class ArticleController {

    /**
     * 文章服务对象
     * 通过Spring自动装配注入，处理文章相关业务逻辑
     */
    @Autowired // Spring自动装配注解，自动注入ArticleService实例
    private ArticleService articleService;

    /**
     * JWT工具类
     * 用于从请求头中解析用户信息
     */
    @Autowired // Spring自动装配注解，自动注入JwtTokenUtil实例
    private JwtTokenUtil jwtTokenUtil;

    /**
     * 创建文章接口
     * 处理创建文章请求，完整路径为 POST /api/article
     *
     * @param req 文章创建请求对象
     * @param request HTTP请求对象，用于获取JWT token
     * @return ApiResponse<Long> API统一响应格式，包含创建成功的文章ID
     */
    @PostMapping // 映射POST请求到 /api/article
    public ApiResponse<Long> createArticle(@RequestBody ArticleCreateReq req, HttpServletRequest request) {
        try {
            // 从JWT token中获取当前用户ID
            Long authorId = getCurrentUserId(request);

            // 调用文章服务创建文章
            Long articleId = articleService.createArticle(req, authorId);

            // 返回成功响应
            return ApiResponse.success(articleId);

        } catch (RuntimeException e) {
            // 业务异常处理：返回业务错误信息
            return ApiResponse.businessError(e.getMessage());

        } catch (Exception e) {
            // 系统异常处理：记录日志并返回系统错误
            e.printStackTrace();
            return ApiResponse.systemError("系统异常，请稍后重试");
        }
    }

    /**
     * 获取文章详情接口
     * 处理获取文章详情请求，完整路径为 GET /api/article/{id}
     *
     * @param id 文章ID
     * @return ApiResponse<ArticleDetailResp> API统一响应格式，包含文章详情信息
     */
    @GetMapping("/{id}") // 映射GET请求到 /api/article/{id}
    public ApiResponse<ArticleDetailResp> getArticleDetail(@PathVariable Long id) {
        try {
            // 调用文章服务获取文章详情
            ArticleDetailResp articleDetail = articleService.getArticleDetail(id);

            // 返回成功响应
            return ApiResponse.success(articleDetail);

        } catch (RuntimeException e) {
            // 业务异常处理：返回业务错误信息
            return ApiResponse.businessError(e.getMessage());

        } catch (Exception e) {
            // 系统异常处理：记录日志并返回系统错误
            e.printStackTrace();
            return ApiResponse.systemError("系统异常，请稍后重试");
        }
    }

    /**
     * 更新文章接口
     * 处理更新文章请求，完整路径为 PUT /api/article
     *
     * @param req 文章更新请求对象
     * @param request HTTP请求对象，用于获取JWT token
     * @return ApiResponse<Boolean> API统一响应格式，包含更新结果
     */
    @PutMapping // 映射PUT请求到 /api/article
    public ApiResponse<Boolean> updateArticle(@RequestBody ArticleUpdateReq req, HttpServletRequest request) {
        try {
            // 从JWT token中获取当前用户ID
            Long authorId = getCurrentUserId(request);

            // 调用文章服务更新文章
            Boolean result = articleService.updateArticle(req, authorId);

            // 返回成功响应
            return ApiResponse.success(result);

        } catch (RuntimeException e) {
            // 业务异常处理：返回业务错误信息
            return ApiResponse.businessError(e.getMessage());

        } catch (Exception e) {
            // 系统异常处理：记录日志并返回系统错误
            e.printStackTrace();
            return ApiResponse.systemError("系统异常，请稍后重试");
        }
    }

    /**
     * 删除文章接口
     * 处理删除文章请求，完整路径为 DELETE /api/article/{id}
     *
     * @param id 文章ID
     * @param request HTTP请求对象，用于获取JWT token
     * @return ApiResponse<Boolean> API统一响应格式，包含删除结果
     */
    @DeleteMapping("/{id}") // 映射DELETE请求到 /api/article/{id}
    public ApiResponse<Boolean> deleteArticle(@PathVariable Long id, HttpServletRequest request) {
        try {
            // 从JWT token中获取当前用户ID
            Long authorId = getCurrentUserId(request);

            // 调用文章服务删除文章
            Boolean result = articleService.deleteArticle(id, authorId);

            // 返回成功响应
            return ApiResponse.success(result);

        } catch (RuntimeException e) {
            // 业务异常处理：返回业务错误信息
            return ApiResponse.businessError(e.getMessage());

        } catch (Exception e) {
            // 系统异常处理：记录日志并返回系统错误
            e.printStackTrace();
            return ApiResponse.systemError("系统异常，请稍后重试");
        }
    }

    /**
     * 发布文章接口
     * 处理发布文章请求，完整路径为 POST /api/article/{id}/publish
     *
     * @param id 文章ID
     * @param request HTTP请求对象，用于获取JWT token
     * @return ApiResponse<Boolean> API统一响应格式，包含发布结果
     */
    @PostMapping("/{id}/publish") // 映射POST请求到 /api/article/{id}/publish
    public ApiResponse<Boolean> publishArticle(@PathVariable Long id, HttpServletRequest request) {
        try {
            // 从JWT token中获取当前用户ID
            Long authorId = getCurrentUserId(request);

            // 调用文章服务发布文章
            Boolean result = articleService.publishArticle(id, authorId);

            // 返回成功响应
            return ApiResponse.success(result);

        } catch (RuntimeException e) {
            // 业务异常处理：返回业务错误信息
            return ApiResponse.businessError(e.getMessage());

        } catch (Exception e) {
            // 系统异常处理：记录日志并返回系统错误
            e.printStackTrace();
            return ApiResponse.systemError("系统异常，请稍后重试");
        }
    }

    /**
     * 取消发布文章接口
     * 处理取消发布文章请求，完整路径为 POST /api/article/{id}/unpublish
     *
     * @param id 文章ID
     * @param request HTTP请求对象，用于获取JWT token
     * @return ApiResponse<Boolean> API统一响应格式，包含取消发布结果
     */
    @PostMapping("/{id}/unpublish") // 映射POST请求到 /api/article/{id}/unpublish
    public ApiResponse<Boolean> unpublishArticle(@PathVariable Long id, HttpServletRequest request) {
        try {
            // 从JWT token中获取当前用户ID
            Long authorId = getCurrentUserId(request);

            // 调用文章服务取消发布文章
            Boolean result = articleService.unpublishArticle(id, authorId);

            // 返回成功响应
            return ApiResponse.success(result);

        } catch (RuntimeException e) {
            // 业务异常处理：返回业务错误信息
            return ApiResponse.businessError(e.getMessage());

        } catch (Exception e) {
            // 系统异常处理：记录日志并返回系统错误
            e.printStackTrace();
            return ApiResponse.systemError("系统异常，请稍后重试");
        }
    }

    /**
     * 点赞文章接口
     * 处理点赞文章请求，完整路径为 POST /api/article/{id}/like
     *
     * @param id 文章ID
     * @return ApiResponse<Boolean> API统一响应格式，包含点赞结果
     */
    @PostMapping("/{id}/like") // 映射POST请求到 /api/article/{id}/like
    public ApiResponse<Boolean> likeArticle(@PathVariable Long id) {
        try {
            // 调用文章服务点赞文章
            Boolean result = articleService.likeArticle(id);

            // 返回成功响应
            return ApiResponse.success(result);

        } catch (RuntimeException e) {
            // 业务异常处理：返回业务错误信息
            return ApiResponse.businessError(e.getMessage());

        } catch (Exception e) {
            // 系统异常处理：记录日志并返回系统错误
            e.printStackTrace();
            return ApiResponse.systemError("系统异常，请稍后重试");
        }
    }

    /**
     * 从HTTP请求中获取当前用户ID
     * 通过解析JWT token获取用户信息
     *
     * @param request HTTP请求对象
     * @return Long 当前用户ID
     * @throws RuntimeException 当token无效或解析失败时抛出异常
     */
    private Long getCurrentUserId(HttpServletRequest request) {
        // 从请求头中获取Authorization字段
        String authHeader = request.getHeader("Authorization");
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            throw new RuntimeException("未提供有效的认证信息");
        }

        // 提取JWT token（去掉"Bearer "前缀）
        String token = authHeader.substring(7);

        try {
            // 从token中解析用户ID
            return jwtTokenUtil.getUserIdFromToken(token);
        } catch (Exception e) {
            throw new RuntimeException("认证信息无效");
        }
    }
}