package top.auunes.demo.controller;

// 导入Spring相关注解
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

// 导入项目相关类
import top.auunes.demo.req.CategoryCreateReq;
import top.auunes.demo.req.CategoryUpdateReq;
import top.auunes.demo.resp.ApiResponse;
import top.auunes.demo.resp.CategoryResp;
import top.auunes.demo.service.CategoryService;

import java.util.List;

/**
 * 分类控制器
 * 处理分类相关的HTTP请求，符合接口文档规范
 * 实现分类管理的全部功能
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@RestController // 标识这是一个REST控制器，返回JSON格式数据
@RequestMapping("/api/category") // 定义控制器的基础路径为 /api/category
public class CategoryController {

    /**
     * 分类服务对象
     * 通过Spring自动装配注入，处理分类相关业务逻辑
     */
    @Autowired // Spring自动装配注解，自动注入CategoryService实例
    private CategoryService categoryService;

    /**
     * 创建分类接口
     * 处理创建分类请求，完整路径为 POST /api/category
     *
     * @param req 分类创建请求对象
     * @return ApiResponse<Long> API统一响应格式，包含创建成功的分类ID
     */
    @PostMapping // 映射POST请求到 /api/category
    public ApiResponse<Long> createCategory(@RequestBody CategoryCreateReq req) {
        try {
            // 调用分类服务创建分类
            Long categoryId = categoryService.createCategory(req);

            // 返回成功响应
            return ApiResponse.success(categoryId);

        } catch (RuntimeException e) {
            // 业务异常处理：返回业务错误信息
            return ApiResponse.businessError(e.getMessage());

        } catch (Exception e) {
            // 系统异常处理：记录日志并返回系统错误
            e.printStackTrace();
            return ApiResponse.systemError("系统异常，请稍后重试");
        }
    }

    /**
     * 获取分类列表接口
     * 处理获取分类列表请求，完整路径为 GET /api/category/list
     *
     * @return ApiResponse<List<CategoryResp>> API统一响应格式，包含分类列表
     */
    @GetMapping("/list") // 映射GET请求到 /api/category/list
    public ApiResponse<List<CategoryResp>> getCategoryList() {
        try {
            // 调用分类服务获取分类列表
            List<CategoryResp> categoryList = categoryService.getCategoryList();

            // 返回成功响应
            return ApiResponse.success(categoryList);

        } catch (RuntimeException e) {
            // 业务异常处理：返回业务错误信息
            return ApiResponse.businessError(e.getMessage());

        } catch (Exception e) {
            // 系统异常处理：记录日志并返回系统错误
            e.printStackTrace();
            return ApiResponse.systemError("系统异常，请稍后重试");
        }
    }

    /**
     * 获取分类详情接口
     * 处理获取分类详情请求，完整路径为 GET /api/category/{id}
     *
     * @param id 分类ID
     * @return ApiResponse<CategoryResp> API统一响应格式，包含分类详情信息
     */
    @GetMapping("/{id}") // 映射GET请求到 /api/category/{id}
    public ApiResponse<CategoryResp> getCategoryDetail(@PathVariable Long id) {
        try {
            // 调用分类服务获取分类详情
            CategoryResp categoryDetail = categoryService.getCategoryDetail(id);

            // 返回成功响应
            return ApiResponse.success(categoryDetail);

        } catch (RuntimeException e) {
            // 业务异常处理：返回业务错误信息
            return ApiResponse.businessError(e.getMessage());

        } catch (Exception e) {
            // 系统异常处理：记录日志并返回系统错误
            e.printStackTrace();
            return ApiResponse.systemError("系统异常，请稍后重试");
        }
    }

    /**
     * 删除分类接口
     * 处理删除分类请求，完整路径为 DELETE /api/category/{id}
     *
     * @param id 分类ID
     * @return ApiResponse<Boolean> API统一响应格式，包含删除结果
     */
    @DeleteMapping("/{id}") // 映射DELETE请求到 /api/category/{id}
    public ApiResponse<Boolean> deleteCategory(@PathVariable Long id) {
        try {
            // 调用分类服务删除分类
            Boolean result = categoryService.deleteCategory(id);

            // 返回成功响应
            return ApiResponse.success(result);

        } catch (RuntimeException e) {
            // 业务异常处理：返回业务错误信息
            return ApiResponse.businessError(e.getMessage());

        } catch (Exception e) {
            // 系统异常处理：记录日志并返回系统错误
            e.printStackTrace();
            return ApiResponse.systemError("系统异常，请稍后重试");
        }
    }

    /**
     * 获取热门分类接口
     * 处理获取热门分类请求，完整路径为 GET /api/category/hot
     *
     * @param limit 限制数量，可选参数，默认为10
     * @return ApiResponse<List<CategoryResp>> API统一响应格式，包含热门分类列表
     */
    @GetMapping("/hot") // 映射GET请求到 /api/category/hot
    public ApiResponse<List<CategoryResp>> getHotCategories(@RequestParam(defaultValue = "10") Integer limit) {
        try {
            // 调用分类服务获取热门分类列表
            List<CategoryResp> hotCategories = categoryService.getHotCategories(limit);

            // 返回成功响应
            return ApiResponse.success(hotCategories);

        } catch (RuntimeException e) {
            // 业务异常处理：返回业务错误信息
            return ApiResponse.businessError(e.getMessage());

        } catch (Exception e) {
            // 系统异常处理：记录日志并返回系统错误
            e.printStackTrace();
            return ApiResponse.systemError("系统异常，请稍后重试");
        }
    }

    /**
     * 检查分类名称是否存在接口
     * 处理检查分类名称请求，完整路径为 GET /api/category/check-name
     * 用于前端实时验证分类名称的唯一性
     *
     * @param name 分类名称
     * @param excludeId 排除的分类ID（更新时使用，可选）
     * @return ApiResponse<Boolean> API统一响应格式，true表示名称已存在，false表示可用
     */
    @GetMapping("/check-name") // 映射GET请求到 /api/category/check-name
    public ApiResponse<Boolean> checkCategoryName(@RequestParam String name,
                                                  @RequestParam(required = false) Long excludeId) {
        try {
            // 调用分类服务检查名称是否存在
            Boolean exists = categoryService.isCategoryNameExists(name, excludeId);

            // 返回成功响应
            return ApiResponse.success(exists);

        } catch (RuntimeException e) {
            // 业务异常处理：返回业务错误信息
            return ApiResponse.businessError(e.getMessage());

        } catch (Exception e) {
            // 系统异常处理：记录日志并返回系统错误
            e.printStackTrace();
            return ApiResponse.systemError("系统异常，请稍后重试");
        }
    }
}