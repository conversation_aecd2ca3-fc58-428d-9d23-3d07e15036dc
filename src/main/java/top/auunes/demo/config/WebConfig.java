package top.auunes.demo.config;

// 导入Spring相关注解和接口
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类
 * 用于注册拦截器、配置静态资源等Web相关配置
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Configuration // 标识这是一个配置类，Spring会自动扫描并加载
public class WebConfig implements WebMvcConfigurer {

    /**
     * JWT拦截器
     * 用于验证JWT Token的有效性
     */
    @Autowired // Spring自动装配注解，注入JWT拦截器实例
    private JwtInterceptor jwtInterceptor;

    /**
     * 添加拦截器配置
     * 配置JWT拦截器的拦截路径和排除路径
     *
     * @param registry 拦截器注册器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(jwtInterceptor) // 注册JWT拦截器
                .addPathPatterns("/api/**") // 拦截所有以/api开头的请求
                .excludePathPatterns(
                        // 排除认证相关接口，这些接口不需要token验证
                        "/api/auth/login",      // 排除登录接口
                        "/api/auth/logout",     // 排除登出接口
                        "/api/auth/register",   // 排除注册接口（如果有的话）

                        // 排除公开接口，这些接口不需要登录就能访问
                        "/api/config/site",     // 排除网站配置接口
                        "/api/article/list",    // 排除文章列表接口（公开访问）
                        "/api/statistics/**",   // 排除统计相关接口（公开访问）

                        // 排除静态资源
                        "/static/**",           // 排除静态资源
                        "/css/**",              // 排除CSS文件
                        "/js/**",               // 排除JavaScript文件
                        "/images/**",           // 排除图片文件
                        "/favicon.ico"          // 排除网站图标
                );
    }

    /**
     * 配置静态资源处理
     * 如果需要自定义静态资源路径，可以在这里配置
     *
     * @param registry 资源处理器注册器
     */
    // @Override
    // public void addResourceHandlers(ResourceHandlerRegistry registry) {
    //     // 示例：配置上传文件的访问路径
    //     registry.addResourceHandler("/uploads/**")
    //             .addResourceLocations("file:uploads/");
    // }

    /**
     * 配置跨域处理
     * 注意：由于已经有CorsConfig类处理跨域，这里不需要重复配置
     * 如果需要更细粒度的跨域控制，可以在这里配置
     *
     * @param registry 跨域配置注册器
     */
    // @Override
    // public void addCorsMappings(CorsRegistry registry) {
    //     // 由于已经有CorsConfig类，这里不需要重复配置
    // }
}