package top.auunes.demo.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * JWT配置类
 */
@Data
@Component
@ConfigurationProperties(prefix = "jwt")
public class JwtConfig {
    /**
     * JWT加解密使用的密钥
     */
    private String secret;

    /**
     * JWT的过期时间
     */
    private long expiration;

    /**
     * JWT的header
     */
    private String header;

} 