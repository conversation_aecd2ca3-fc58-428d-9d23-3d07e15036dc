package top.auunes.demo.config;

// 导入Spring配置注解

import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 跨域资源共享(CORS)配置类
 * 用于解决前后端分离项目中的跨域问题
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Configuration // 标识这是一个配置类，Spring会自动扫描并加载
public class CorsConfig implements WebMvcConfigurer {

    /**
     * 添加CORS映射配置
     * 重写WebMvcConfigurer接口的方法，配置跨域访问规则
     *
     * @param registry CORS注册器，用于注册跨域配置
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**") // 对所有路径生效（/**表示匹配所有路径）
                .allowedOriginPatterns("*") // 允许所有来源的跨域请求（*表示任意域名）
                .allowedHeaders(CorsConfiguration.ALL) // 允许所有请求头
                .allowedMethods(CorsConfiguration.ALL) // 允许所有HTTP方法（GET、POST、PUT、DELETE等）
                .allowCredentials(true) // 允许发送Cookie和认证信息
                .maxAge(3600); // 预检请求的缓存时间为3600秒（1小时内不需要再发送OPTIONS预检请求）
    }

}
