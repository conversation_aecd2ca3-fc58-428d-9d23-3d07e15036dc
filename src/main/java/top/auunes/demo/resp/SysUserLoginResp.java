package top.auunes.demo.resp;

/**
 * 系统用户登录响应对象
 * 用于返回用户登录成功后的用户信息
 * 注意：实际项目中不应该返回密码信息，这里仅作演示
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public class SysUserLoginResp {

    /**
     * 用户唯一标识ID
     * 用户的主键ID
     */
    private Long id;

    /**
     * 用户登录名
     * 用户的登录标识
     */
    private String LoginName;

    /**
     * 用户密码
     * 注意：实际项目中不应该在响应中返回密码信息
     * 这里仅作演示，建议移除此字段
     */
    private String password;

    /**
     * 获取用户ID
     *
     * @return Long 用户ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置用户ID
     *
     * @param id 用户ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取用户登录名
     *
     * @return String 用户登录名
     */
    public String getLoginName() {
        return LoginName;
    }

    /**
     * 设置用户登录名
     *
     * @param loginName 用户登录名
     */
    public void setLoginName(String loginName) {
        LoginName = loginName;
    }

    /**
     * 获取用户密码
     * 注意：实际项目中不应该提供此方法
     *
     * @return String 用户密码
     */
    public String getPassword() {
        return password;
    }

    /**
     * 设置用户密码
     * 注意：实际项目中不应该提供此方法
     *
     * @param password 用户密码
     */
    public void setPassword(String password) {
        this.password = password;
    }

    /**
     * 重写toString方法
     * 用于对象的字符串表示，便于调试和日志输出
     * 注意：为了安全考虑，实际项目中不应该在日志中输出密码信息
     *
     * @return String 对象的字符串表示
     */
    @Override
    public String toString() {
        return "SysUserLoginResp{" +
                "id=" + id +
                ", LoginName='" + LoginName + '\'' +
                ", password='" + password + '\'' +
                '}';
    }
}
