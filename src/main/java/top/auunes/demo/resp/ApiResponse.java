package top.auunes.demo.resp;

/**
 * API统一响应格式
 * 根据接口文档定义的响应格式
 * 
 * @param <T> 响应数据的类型
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public class ApiResponse<T> {

    /**
     * 错误码
     * 0 = 成功，无异常
     * 500 = 系统异常
     * 401 = 需要登录
     * 其他值 = 业务异常
     */
    private Integer error = 0;

    /**
     * 响应数据内容
     * 业务数据对象
     */
    private T body;

    /**
     * 响应消息
     * 错误信息或成功提示
     */
    private String message = "";

    /**
     * 默认构造函数
     * 创建成功响应
     */
    public ApiResponse() {
        this.error = 0;
        this.message = "";
    }

    /**
     * 成功响应构造函数
     * 
     * @param body 响应数据
     */
    public ApiResponse(T body) {
        this.error = 0;
        this.body = body;
        this.message = "";
    }

    /**
     * 错误响应构造函数
     * 
     * @param error 错误码
     * @param message 错误消息
     */
    public ApiResponse(Integer error, String message) {
        this.error = error;
        this.message = message;
        this.body = null;
    }

    /**
     * 创建成功响应
     * 
     * @param body 响应数据
     * @param <T> 数据类型
     * @return ApiResponse 成功响应对象
     */
    public static <T> ApiResponse<T> success(T body) {
        return new ApiResponse<>(body);
    }

    /**
     * 创建成功响应（无数据）
     * 
     * @param <T> 数据类型
     * @return ApiResponse 成功响应对象
     */
    public static <T> ApiResponse<T> success() {
        return new ApiResponse<>();
    }

    /**
     * 创建错误响应
     * 
     * @param error 错误码
     * @param message 错误消息
     * @param <T> 数据类型
     * @return ApiResponse 错误响应对象
     */
    public static <T> ApiResponse<T> error(Integer error, String message) {
        return new ApiResponse<>(error, message);
    }

    /**
     * 创建业务异常响应
     * 
     * @param message 错误消息
     * @param <T> 数据类型
     * @return ApiResponse 业务异常响应对象
     */
    public static <T> ApiResponse<T> businessError(String message) {
        return new ApiResponse<>(400, message);
    }

    /**
     * 创建系统异常响应
     * 
     * @param message 错误消息
     * @param <T> 数据类型
     * @return ApiResponse 系统异常响应对象
     */
    public static <T> ApiResponse<T> systemError(String message) {
        return new ApiResponse<>(500, message);
    }

    /**
     * 创建需要登录响应
     * 
     * @param message 错误消息
     * @param <T> 数据类型
     * @return ApiResponse 需要登录响应对象
     */
    public static <T> ApiResponse<T> unauthorized(String message) {
        return new ApiResponse<>(401, message);
    }

    // Getter和Setter方法

    /**
     * 获取错误码
     * 
     * @return Integer 错误码
     */
    public Integer getError() {
        return error;
    }

    /**
     * 设置错误码
     * 
     * @param error 错误码
     */
    public void setError(Integer error) {
        this.error = error;
    }

    /**
     * 获取响应数据
     * 
     * @return T 响应数据
     */
    public T getBody() {
        return body;
    }

    /**
     * 设置响应数据
     * 
     * @param body 响应数据
     */
    public void setBody(T body) {
        this.body = body;
    }

    /**
     * 获取响应消息
     * 
     * @return String 响应消息
     */
    public String getMessage() {
        return message;
    }

    /**
     * 设置响应消息
     * 
     * @param message 响应消息
     */
    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * 重写toString方法
     * 用于对象的字符串表示，便于调试和日志输出
     * 
     * @return String 对象的字符串表示
     */
    @Override
    public String toString() {
        return "ApiResponse{" +
                "error=" + error +
                ", body=" + body +
                ", message='" + message + '\'' +
                '}';
    }
}
