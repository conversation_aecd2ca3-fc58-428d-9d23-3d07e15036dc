package top.auunes.demo.resp;

import java.util.List;

/**
 * 分页响应对象
 * 用于统一分页查询的响应格式
 *
 * @param <T> 数据类型
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public class PageResp<T> {

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 数据列表
     */
    private List<T> list;

    /**
     * 默认构造函数
     */
    public PageResp() {
    }

    /**
     * 构造函数
     *
     * @param total 总记录数
     * @param list 数据列表
     */
    public PageResp(Long total, List<T> list) {
        this.total = total;
        this.list = list;
    }

    /**
     * 获取总记录数
     *
     * @return Long 总记录数
     */
    public Long getTotal() {
        return total;
    }

    /**
     * 设置总记录数
     *
     * @param total 总记录数
     */
    public void setTotal(Long total) {
        this.total = total;
    }

    /**
     * 获取数据列表
     *
     * @return List<T> 数据列表
     */
    public List<T> getList() {
        return list;
    }

    /**
     * 设置数据列表
     *
     * @param list 数据列表
     */
    public void setList(List<T> list) {
        this.list = list;
    }

    /**
     * 重写toString方法
     * 用于对象的字符串表示，便于调试和日志输出
     *
     * @return String 对象的字符串表示
     */
    @Override
    public String toString() {
        return "PageResp{" +
                "total=" + total +
                ", list=" + list +
                '}';
    }
}
