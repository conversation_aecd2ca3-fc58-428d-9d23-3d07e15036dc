package top.auunes.demo.resp;

import java.time.LocalDateTime;

/**
 * 标签响应对象
 * 根据接口文档定义的标签响应格式
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public class TagResp {

    /**
     * 标签ID
     */
    private Long id;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 标签颜色
     * 用于前端显示标签的颜色
     */
    private String color;

    /**
     * 文章数量
     * 该标签下的文章总数
     */
    private Integer articleCount;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 默认构造函数
     */
    public TagResp() {
    }

    /**
     * 获取标签ID
     *
     * @return Long 标签ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置标签ID
     *
     * @param id 标签ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取标签名称
     *
     * @return String 标签名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置标签名称
     *
     * @param name 标签名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取标签颜色
     *
     * @return String 标签颜色
     */
    public String getColor() {
        return color;
    }

    /**
     * 设置标签颜色
     *
     * @param color 标签颜色
     */
    public void setColor(String color) {
        this.color = color;
    }

    /**
     * 获取文章数量
     *
     * @return Integer 文章数量
     */
    public Integer getArticleCount() {
        return articleCount;
    }

    /**
     * 设置文章数量
     *
     * @param articleCount 文章数量
     */
    public void setArticleCount(Integer articleCount) {
        this.articleCount = articleCount;
    }

    /**
     * 获取创建时间
     *
     * @return LocalDateTime 创建时间
     */
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return LocalDateTime 更新时间
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 重写toString方法
     * 用于对象的字符串表示，便于调试和日志输出
     *
     * @return String 对象的字符串表示
     */
    @Override
    public String toString() {
        return "TagResp{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", color='" + color + '\'' +
                ", articleCount=" + articleCount +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
