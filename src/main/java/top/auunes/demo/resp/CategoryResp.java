package top.auunes.demo.resp;

import java.time.LocalDateTime;

/**
 * 分类响应对象
 * 根据接口文档定义的分类响应格式
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public class CategoryResp {

    /**
     * 分类ID
     */
    private Long id;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 分类描述
     */
    private String description;

    /**
     * 排序值
     * 数值越小排序越靠前
     */
    private Integer sortOrder;

    /**
     * 文章数量
     * 该分类下的文章总数
     */
    private Integer articleCount;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 默认构造函数
     */
    public CategoryResp() {
    }

    /**
     * 获取分类ID
     *
     * @return Long 分类ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置分类ID
     *
     * @param id 分类ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取分类名称
     *
     * @return String 分类名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置分类名称
     *
     * @param name 分类名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取分类描述
     *
     * @return String 分类描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 设置分类描述
     *
     * @param description 分类描述
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 获取排序值
     *
     * @return Integer 排序值
     */
    public Integer getSortOrder() {
        return sortOrder;
    }

    /**
     * 设置排序值
     *
     * @param sortOrder 排序值
     */
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    /**
     * 获取文章数量
     *
     * @return Integer 文章数量
     */
    public Integer getArticleCount() {
        return articleCount;
    }

    /**
     * 设置文章数量
     *
     * @param articleCount 文章数量
     */
    public void setArticleCount(Integer articleCount) {
        this.articleCount = articleCount;
    }

    /**
     * 获取创建时间
     *
     * @return LocalDateTime 创建时间
     */
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return LocalDateTime 更新时间
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 重写toString方法
     * 用于对象的字符串表示，便于调试和日志输出
     *
     * @return String 对象的字符串表示
     */
    @Override
    public String toString() {
        return "CategoryResp{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", sortOrder=" + sortOrder +
                ", articleCount=" + articleCount +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}