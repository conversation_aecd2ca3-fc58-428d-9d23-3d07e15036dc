package top.auunes.demo.resp;

/**
 * 通用响应对象
 * 用于统一API响应格式，包含操作结果、消息和数据内容
 * 使用泛型T来支持不同类型的数据返回
 *
 * @param <T> 响应数据的类型
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public class CommonResp<T> {

    /**
     * 业务操作成功标识
     * true表示操作成功，false表示操作失败
     * 默认值为true
     */
    private Boolean success = true;

    /**
     * 响应消息
     * 用于返回操作结果的描述信息，如错误信息或成功提示
     */
    private String message;

    /**
     * 响应数据内容
     * 使用泛型T，可以返回任意类型的数据对象
     */
    private T content;

    /**
     * 获取操作成功标识
     *
     * @return Boolean 操作是否成功
     */
    public Boolean getSuccess() {
        return success;
    }

    /**
     * 设置操作成功标识
     *
     * @param success 操作是否成功
     */
    public void setSuccess(Boolean success) {
        this.success = success;
    }

    /**
     * 获取响应消息
     *
     * @return String 响应消息
     */
    public String getMessage() {
        return message;
    }

    /**
     * 设置响应消息
     *
     * @param message 响应消息
     */
    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * 获取响应数据内容
     *
     * @return T 响应数据内容
     */
    public T getContent() {
        return content;
    }

    /**
     * 设置响应数据内容
     *
     * @param content 响应数据内容
     */
    public void setContent(T content) {
        this.content = content;
    }

    /**
     * 重写toString方法
     * 用于对象的字符串表示，便于调试和日志输出
     *
     * @return String 对象的字符串表示
     */
    @Override
    public String toString() {
        return "CommonResp{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", content=" + content +
                '}';
    }
}
