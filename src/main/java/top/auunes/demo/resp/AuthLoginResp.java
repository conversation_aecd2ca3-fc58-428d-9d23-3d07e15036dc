package top.auunes.demo.resp;

/**
 * 认证登录响应对象
 * 根据接口文档定义的登录响应格式
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public class AuthLoginResp {

    /**
     * JWT Token
     * 用户登录成功后生成的认证令牌
     */
    private String token;

    /**
     * 用户名
     * 用户的登录名
     */
    private String username;

    /**
     * 用户ID
     * 用户的唯一标识
     */
    private Long userId;

    /**
     * 用户昵称
     * 用户的显示名称
     */
    private String nickname;

    /**
     * 用户头像
     * 用户头像的URL地址
     */
    private String avatar;

    /**
     * 默认构造函数
     */
    public AuthLoginResp() {
    }

    /**
     * 完整构造函数
     * 
     * @param token JWT令牌
     * @param username 用户名
     * @param userId 用户ID
     * @param nickname 用户昵称
     * @param avatar 用户头像URL
     */
    public AuthLoginResp(String token, String username, Long userId, String nickname, String avatar) {
        this.token = token;
        this.username = username;
        this.userId = userId;
        this.nickname = nickname;
        this.avatar = avatar;
    }

    /**
     * 获取JWT Token
     * 
     * @return String JWT Token
     */
    public String getToken() {
        return token;
    }

    /**
     * 设置JWT Token
     * 
     * @param token JWT Token
     */
    public void setToken(String token) {
        this.token = token;
    }

    /**
     * 获取用户名
     * 
     * @return String 用户名
     */
    public String getUsername() {
        return username;
    }

    /**
     * 设置用户名
     * 
     * @param username 用户名
     */
    public void setUsername(String username) {
        this.username = username;
    }

    /**
     * 获取用户ID
     * 
     * @return Long 用户ID
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * 设置用户ID
     * 
     * @param userId 用户ID
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * 获取用户昵称
     * 
     * @return String 用户昵称
     */
    public String getNickname() {
        return nickname;
    }

    /**
     * 设置用户昵称
     * 
     * @param nickname 用户昵称
     */
    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    /**
     * 获取用户头像URL
     * 
     * @return String 用户头像URL
     */
    public String getAvatar() {
        return avatar;
    }

    /**
     * 设置用户头像URL
     * 
     * @param avatar 用户头像URL
     */
    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    /**
     * 重写toString方法
     * 用于对象的字符串表示，便于调试和日志输出
     * 注意：为了安全考虑，不输出token信息
     * 
     * @return String 对象的字符串表示
     */
    @Override
    public String toString() {
        return "AuthLoginResp{" +
                "username='" + username + '\'' +
                ", userId=" + userId +
                ", nickname='" + nickname + '\'' +
                ", avatar='" + avatar + '\'' +
                ", token='[HIDDEN]'" +
                '}';
    }
}
