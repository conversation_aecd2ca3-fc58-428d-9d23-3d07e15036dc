server:
  port: 8081
spring:
  datasource:
    url: ****************************************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver

# JWT配置
jwt:
  # JWT加解密使用的密钥（建议使用复杂的密钥）
  secret: mySecretKey123456789abcdefghijklmnopqrstuvwxyz
  # JWT过期时间（单位：秒，7200秒 = 2小时）
  expiration: 7200
  # JWT在请求头中的字段名
  header: Authorization
