package top.auunes.demo.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import top.auunes.demo.req.CategoryCreateReq;
import top.auunes.demo.req.CategoryUpdateReq;
import top.auunes.demo.resp.CategoryResp;

import javax.annotation.Resource;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 分类服务测试类
 * 测试分类相关的业务逻辑
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@SpringBootTest
@ActiveProfiles("test") // 使用测试配置文件
public class CategoryServiceTest {

    @Resource
    private CategoryService categoryService;

    /**
     * 测试创建分类
     */
    @Test
    public void testCreateCategory() {
        CategoryCreateReq req = new CategoryCreateReq();
        req.setName("测试分类");
        req.setDescription("这是一个测试分类");
        req.setSortOrder(1);

        try {
            Long categoryId = categoryService.createCategory(req);
            assertNotNull(categoryId);
            assertTrue(categoryId > 0);
            System.out.println("创建分类成功，ID: " + categoryId);
        } catch (Exception e) {
            System.out.println("创建分类失败: " + e.getMessage());
        }
    }

    /**
     * 测试获取分类列表
     */
    @Test
    public void testGetCategoryList() {
        try {
            List<CategoryResp> categories = categoryService.getCategoryList();
            assertNotNull(categories);
            System.out.println("获取到 " + categories.size() + " 个分类");

            for (CategoryResp category : categories) {
                System.out.println("分类: " + category.getName() +
                                 ", 描述: " + category.getDescription() +
                                 ", 文章数量: " + category.getArticleCount());
            }
        } catch (Exception e) {
            System.out.println("获取分类列表失败: " + e.getMessage());
        }
    }

    /**
     * 测试检查分类名称是否存在
     */
    @Test
    public void testIsCategoryNameExists() {
        try {
            Boolean exists = categoryService.isCategoryNameExists("技术分享", null);
            System.out.println("分类名称'技术分享'是否存在: " + exists);
            
            Boolean notExists = categoryService.isCategoryNameExists("不存在的分类", null);
            System.out.println("分类名称'不存在的分类'是否存在: " + notExists);
        } catch (Exception e) {
            System.out.println("检查分类名称失败: " + e.getMessage());
        }
    }

    /**
     * 测试获取热门分类
     */
    @Test
    public void testGetHotCategories() {
        try {
            List<CategoryResp> hotCategories = categoryService.getHotCategories(5);
            assertNotNull(hotCategories);
            
            System.out.println("热门分类列表:");
            for (CategoryResp category : hotCategories) {
                System.out.println("分类: " + category.getName() + 
                                 ", 文章数量: " + category.getArticleCount());
            }
        } catch (Exception e) {
            System.out.println("获取热门分类失败: " + e.getMessage());
        }
    }
}
