# 个人博客系统数据库文档

## 数据库概述

本文档详细描述了个人博客系统所使用的数据库结构，包括表设计、字段定义、索引、约束条件等信息。

## 数据库环境

- 数据库类型: MySQL
- 数据库版本: 8.0及以上

## 创建数据库

```sql
-- 创建数据库
CREATE DATABASE IF NOT EXISTS personal_blog DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 使用数据库
USE personal_blog;
```

## 表结构设计

### 1. 用户表 (user)

存储博客系统用户信息。

```sql
CREATE TABLE `user` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `nickname` varchar(50) NOT NULL COMMENT '昵称',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `bio` text DEFAULT NULL COMMENT '个人简介',
  `website` varchar(255) DEFAULT NULL COMMENT '个人网站',
  `location` varchar(100) DEFAULT NULL COMMENT '所在地',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 初始用户数据
INSERT INTO `user` (`username`, `password`, `nickname`, `email`, `create_time`, `update_time`) 
VALUES ('blogger', 'e10adc3949ba59abbe56e057f20f883e', '博主', '<EMAIL>', NOW(), NOW());  -- 密码：123456的MD5值
```

### 2. 分类表 (category)

存储文章分类信息。

```sql
CREATE TABLE `category` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `description` text DEFAULT NULL COMMENT '分类描述',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序顺序',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分类表';

-- 示例数据
INSERT INTO `category` (`name`, `description`, `sort_order`, `create_time`, `update_time`) 
VALUES 
('技术分享', '技术相关的文章分享', 1, NOW(), NOW()),
('生活随笔', '记录生活点滴', 2, NOW(), NOW()),
('学习笔记', '学习过程中的笔记整理', 3, NOW(), NOW()),
('项目总结', '项目开发经验总结', 4, NOW(), NOW());
```

### 3. 标签表 (tag)

存储文章标签信息。

```sql
CREATE TABLE `tag` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(50) NOT NULL COMMENT '标签名称',
  `color` varchar(20) DEFAULT '#007bff' COMMENT '标签颜色',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签表';

-- 示例数据
INSERT INTO `tag` (`name`, `color`, `create_time`, `update_time`) 
VALUES 
('Java', '#ff6b6b', NOW(), NOW()),
('Spring Boot', '#4ecdc4', NOW(), NOW()),
('MySQL', '#45b7d1', NOW(), NOW()),
('Vue.js', '#96ceb4', NOW(), NOW()),
('JavaScript', '#feca57', NOW(), NOW());
```

### 4. 文章表 (article)

存储博客文章信息。

```sql
CREATE TABLE `article` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(200) NOT NULL COMMENT '文章标题',
  `content` longtext NOT NULL COMMENT '文章内容',
  `summary` text DEFAULT NULL COMMENT '文章摘要',
  `cover_image` varchar(255) DEFAULT NULL COMMENT '封面图片URL',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `author_id` int(11) NOT NULL COMMENT '作者ID',
  `status` varchar(20) NOT NULL DEFAULT 'draft' COMMENT '状态：draft-草稿，published-已发布',
  `view_count` int(11) NOT NULL DEFAULT 0 COMMENT '浏览次数',
  `like_count` int(11) NOT NULL DEFAULT 0 COMMENT '点赞次数',
  `comment_count` int(11) NOT NULL DEFAULT 0 COMMENT '评论次数',
  `is_top` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否置顶：1-是，0-否',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_author_id` (`author_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_publish_time` (`publish_time`),
  KEY `idx_is_top` (`is_top`),
  CONSTRAINT `fk_article_category` FOREIGN KEY (`category_id`) REFERENCES `category` (`id`),
  CONSTRAINT `fk_article_author` FOREIGN KEY (`author_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章表';

-- 示例数据
INSERT INTO `article` (`title`, `content`, `summary`, `category_id`, `author_id`, `status`, `publish_time`, `create_time`, `update_time`) 
VALUES 
('我的第一篇博客', '这是我的第一篇博客内容...', '欢迎来到我的博客', 1, 1, 'published', NOW(), NOW(), NOW()),
('Spring Boot入门指南', 'Spring Boot是一个优秀的Java框架...', 'Spring Boot学习笔记', 3, 1, 'published', NOW(), NOW(), NOW());
```

### 5. 文章标签关联表 (article_tag)

存储文章与标签的多对多关联关系。

```sql
CREATE TABLE `article_tag` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `article_id` int(11) NOT NULL COMMENT '文章ID',
  `tag_id` int(11) NOT NULL COMMENT '标签ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_article_tag` (`article_id`, `tag_id`),
  KEY `idx_article_id` (`article_id`),
  KEY `idx_tag_id` (`tag_id`),
  CONSTRAINT `fk_article_tag_article` FOREIGN KEY (`article_id`) REFERENCES `article` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_article_tag_tag` FOREIGN KEY (`tag_id`) REFERENCES `tag` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章标签关联表';

-- 示例数据
INSERT INTO `article_tag` (`article_id`, `tag_id`, `create_time`) 
VALUES 
(1, 1, NOW()),
(2, 1, NOW()),
(2, 2, NOW());
```

### 6. 评论表 (comment)

存储文章评论信息。

```sql
CREATE TABLE `comment` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `article_id` int(11) NOT NULL COMMENT '文章ID',
  `user_id` int(11) NOT NULL COMMENT '评论用户ID',
  `parent_id` int(11) NOT NULL DEFAULT 0 COMMENT '父评论ID，0表示顶级评论',
  `reply_to_user_id` int(11) NOT NULL DEFAULT 0 COMMENT '回复的用户ID，0表示不是回复',
  `content` text NOT NULL COMMENT '评论内容',
  `like_count` int(11) NOT NULL DEFAULT 0 COMMENT '点赞次数',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常，0-已删除',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_article_id` (`article_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_comment_article` FOREIGN KEY (`article_id`) REFERENCES `article` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_comment_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论表';
```

### 7. 文章点赞表 (article_like)

存储用户对文章的点赞记录。

```sql
CREATE TABLE `article_like` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `article_id` int(11) NOT NULL COMMENT '文章ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_article_user` (`article_id`, `user_id`),
  KEY `idx_article_id` (`article_id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `fk_article_like_article` FOREIGN KEY (`article_id`) REFERENCES `article` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_article_like_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章点赞表';
```

### 8. 文章收藏表 (article_favorite)

存储用户对文章的收藏记录。

```sql
CREATE TABLE `article_favorite` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `article_id` int(11) NOT NULL COMMENT '文章ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_article_user` (`article_id`, `user_id`),
  KEY `idx_article_id` (`article_id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `fk_article_favorite_article` FOREIGN KEY (`article_id`) REFERENCES `article` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_article_favorite_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章收藏表';
```

### 9. 评论点赞表 (comment_like)

存储用户对评论的点赞记录。

```sql
CREATE TABLE `comment_like` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `comment_id` int(11) NOT NULL COMMENT '评论ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_comment_user` (`comment_id`, `user_id`),
  KEY `idx_comment_id` (`comment_id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `fk_comment_like_comment` FOREIGN KEY (`comment_id`) REFERENCES `comment` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_comment_like_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论点赞表';
```

### 10. 网站配置表 (site_config)

存储网站基本配置信息。

```sql
CREATE TABLE `site_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text DEFAULT NULL COMMENT '配置值',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网站配置表';

-- 初始配置数据
INSERT INTO `site_config` (`config_key`, `config_value`, `description`, `create_time`, `update_time`)
VALUES
('site_name', '我的个人博客', '网站名称', NOW(), NOW()),
('site_description', '记录技术与生活', '网站描述', NOW(), NOW()),
('site_keywords', '博客,技术,生活', '网站关键词', NOW(), NOW()),
('site_logo', '', '网站Logo URL', NOW(), NOW()),
('site_icon', '', '网站图标URL', NOW(), NOW()),
('author_name', '博主姓名', '作者姓名', NOW(), NOW()),
('author_email', '<EMAIL>', '联系邮箱', NOW(), NOW()),
('icp', '', '备案号', NOW(), NOW()),
('copyright', '© 2024 我的个人博客. All rights reserved.', '版权信息', NOW(), NOW());
```

### 11. 文章访问记录表 (article_view)

存储文章访问记录，用于统计分析。

```sql
CREATE TABLE `article_view` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `article_id` int(11) NOT NULL COMMENT '文章ID',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID，未登录用户为NULL',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text DEFAULT NULL COMMENT '用户代理信息',
  `view_date` date NOT NULL COMMENT '访问日期',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_article_id` (`article_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_view_date` (`view_date`),
  KEY `idx_ip_address` (`ip_address`),
  CONSTRAINT `fk_article_view_article` FOREIGN KEY (`article_id`) REFERENCES `article` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_article_view_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章访问记录表';
```

## 数据库表关系

1. **用户与文章关系**
   - 用户表(user)与文章表(article)通过外键`author_id`关联
   - 一个用户可以发布多篇文章
   - 每篇文章只属于一个作者

2. **分类与文章关系**
   - 分类表(category)与文章表(article)通过外键`category_id`关联
   - 一个分类可以包含多篇文章
   - 每篇文章只属于一个分类

3. **标签与文章关系**
   - 标签表(tag)与文章表(article)通过中间表`article_tag`建立多对多关系
   - 一篇文章可以有多个标签
   - 一个标签可以被多篇文章使用

4. **文章与评论关系**
   - 文章表(article)与评论表(comment)通过外键`article_id`关联
   - 一篇文章可以有多条评论
   - 每条评论只属于一篇文章

5. **用户与评论关系**
   - 用户表(user)与评论表(comment)通过外键`user_id`关联
   - 一个用户可以发表多条评论
   - 每条评论只属于一个用户

6. **评论层级关系**
   - 评论表(comment)通过`parent_id`字段实现自关联
   - `parent_id`为0表示顶级评论
   - 其他值表示回复某条评论

7. **点赞和收藏关系**
   - 文章点赞表(article_like)记录用户对文章的点赞
   - 文章收藏表(article_favorite)记录用户对文章的收藏
   - 评论点赞表(comment_like)记录用户对评论的点赞
   - 通过唯一索引防止重复点赞/收藏

## 数据库用户权限

创建专用的数据库用户，并授予相应的权限：

```sql
-- 创建数据库用户
CREATE USER 'blog_user'@'%' IDENTIFIED BY 'blog_password';

-- 授予权限
GRANT ALL PRIVILEGES ON personal_blog.* TO 'blog_user'@'%';

-- 刷新权限
FLUSH PRIVILEGES;
```

## 索引优化建议

1. **文章表索引**
   - 为`status`、`create_time`、`publish_time`、`is_top`字段建立索引，优化文章列表查询
   - 为`category_id`、`author_id`建立索引，优化关联查询

2. **评论表索引**
   - 为`article_id`、`parent_id`建立索引，优化评论查询
   - 为`create_time`建立索引，优化按时间排序

3. **访问记录表索引**
   - 为`view_date`建立索引，优化统计查询
   - 为`article_id`建立索引，优化文章访问统计

## 注意事项

1. 线上环境部署时，请修改数据库用户密码为强密码
2. 为保证数据安全，建议定期备份数据库
3. 生产环境中，请配置数据库的字符集为utf8mb4，以支持完整的Unicode字符
4. 密码字段存储的是MD5加密后的值，建议在生产环境中使用更安全的加密方式
5. 建库顺序必须按照文档中表顺序执行，否则外键约束会报错：
   - 先创建基础表：user、category、tag
   - 再创建关联表：article、comment
   - 最后创建关系表：article_tag、article_like、article_favorite、comment_like
6. 文章访问记录表(article_view)数据量可能较大，建议定期清理历史数据或进行分表处理
7. 为提高查询性能，建议对经常查询的字段建立合适的索引
8. 在删除文章或用户时，相关的点赞、收藏、评论记录会通过外键约束自动删除
