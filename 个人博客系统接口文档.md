# 个人博客系统 API 文档

## 通用说明

### 请求头要求
- header中必须设置 auth， 值为当前登录后保存的token值

### 请求格式
- 请求的参数使用JSON格式，即使参数为空，也需要使用 `{}` 来代替
- 请求方式默认是POST，除非有明确要求

### 响应格式
后端统一返回的参数为JSON对象，格式如下：
```json
{
    "error": 0,
    "body": object,
    "message": ""
}
```

- error = 0, 表示没有任何异常
- error = 500, 表示系统异常，需要弹出系统异常的错误
- error = 401，表示需要登录
- error 其它值，表示业务异常，直接弹出 message内容
- body 是一个对象

## 用户认证

### 用户登录
- 接口功能：用户登录系统获取token
- 接口地址: `/api/auth/login`
- 方法: POST
- 需要登录: 否
- 请求参数:
```json
{
    "username": "用户名",
    "password": "密码"
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "username": "blogger",
        "userId": 1,
        "nickname": "博主昵称",
        "avatar": "头像URL"
    },
    "message": ""
}
```

### 用户注册
- 接口功能：新用户注册
- 接口地址: `/api/auth/register`
- 方法: POST
- 需要登录: 否
- 请求参数:
```json
{
    "username": "用户名",
    "password": "密码",
    "email": "邮箱地址",
    "nickname": "昵称"
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {
        "userId": 1
    },
    "message": "注册成功"
}
```

### 用户登出
- 接口功能：用户登出系统
- 接口地址: `/api/auth/logout`
- 方法: POST
- 需要登录: 是
- 请求参数:
```json
{}
```
- 返回值:
```json
{
    "error": 0,
    "body": {},
    "message": "登出成功"
}
```

## 博客文章管理

### 获取文章列表
- 接口功能：分页获取博客文章列表
- 接口地址: `/api/article/list`
- 方法: POST
- 需要登录: 否
- 请求参数:
```json
{
    "pageNum": 1,
    "pageSize": 10,
    "title": "文章标题（可选）",
    "categoryId": 1, // 分类ID（可选）
    "tagId": 1, // 标签ID（可选）
    "status": "published" // 文章状态（可选）：draft/published
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {
        "total": 50,
        "list": [
            {
                "id": 1,
                "title": "我的第一篇博客",
                "summary": "这是文章摘要...",
                "coverImage": "封面图片URL",
                "categoryId": 1,
                "categoryName": "技术分享",
                "tags": [
                    {
                        "id": 1,
                        "name": "Java"
                    }
                ],
                "authorId": 1,
                "authorName": "博主昵称",
                "viewCount": 100,
                "likeCount": 10,
                "commentCount": 5,
                "status": "published",
                "createTime": "2024-01-01 10:00:00",
                "updateTime": "2024-01-01 10:00:00"
            }
            // 更多文章记录...
        ]
    },
    "message": ""
}
```

### 获取文章详情
- 接口功能：根据ID获取文章详细内容
- 接口地址: `/api/article/detail`
- 方法: POST
- 需要登录: 否
- 请求参数:
```json
{
    "id": 1
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {
        "id": 1,
        "title": "我的第一篇博客",
        "content": "文章正文内容...",
        "summary": "这是文章摘要...",
        "coverImage": "封面图片URL",
        "categoryId": 1,
        "categoryName": "技术分享",
        "tags": [
            {
                "id": 1,
                "name": "Java"
            }
        ],
        "authorId": 1,
        "authorName": "博主昵称",
        "authorAvatar": "作者头像URL",
        "viewCount": 100,
        "likeCount": 10,
        "commentCount": 5,
        "status": "published",
        "createTime": "2024-01-01 10:00:00",
        "updateTime": "2024-01-01 10:00:00"
    },
    "message": ""
}
```

### 发布文章
- 接口功能：发布新文章
- 接口地址: `/api/article/add`
- 方法: POST
- 需要登录: 是
- 请求参数:
```json
{
    "title": "文章标题",
    "content": "文章正文内容",
    "summary": "文章摘要",
    "coverImage": "封面图片URL",
    "categoryId": 1,
    "tagIds": [1, 2, 3],
    "status": "published" // draft/published
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {
        "id": 1
    },
    "message": "发布成功"
}
```

### 更新文章
- 接口功能：更新文章信息
- 接口地址: `/api/article/update`
- 方法: POST
- 需要登录: 是
- 请求参数:
```json
{
    "id": 1,
    "title": "更新后的文章标题",
    "content": "更新后的文章正文内容",
    "summary": "更新后的文章摘要",
    "coverImage": "封面图片URL",
    "categoryId": 1,
    "tagIds": [1, 2, 3],
    "status": "published"
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {},
    "message": "更新成功"
}
```

### 删除文章
- 接口功能：删除文章
- 接口地址: `/api/article/delete`
- 方法: POST
- 需要登录: 是
- 请求参数:
```json
{
    "id": 1
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {},
    "message": "删除成功"
}
```

### 获取我的文章列表
- 接口功能：获取当前用户的文章列表
- 接口地址: `/api/article/my-list`
- 方法: POST
- 需要登录: 是
- 请求参数:
```json
{
    "pageNum": 1,
    "pageSize": 10,
    "title": "文章标题（可选）",
    "status": "published" // 文章状态（可选）
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {
        "total": 20,
        "list": [
            {
                "id": 1,
                "title": "我的第一篇博客",
                "summary": "这是文章摘要...",
                "coverImage": "封面图片URL",
                "categoryName": "技术分享",
                "viewCount": 100,
                "likeCount": 10,
                "commentCount": 5,
                "status": "published",
                "createTime": "2024-01-01 10:00:00",
                "updateTime": "2024-01-01 10:00:00"
            }
            // 更多文章记录...
        ]
    },
    "message": ""
}
```

## 分类管理

### 获取所有分类列表
- 接口功能：获取所有分类信息列表（不分页）
- 接口地址: `/api/category/list/all`
- 方法: GET
- 需要登录: 否
- 请求参数: 无
- 返回值:
```json
{
    "error": 0,
    "body": [
        {
            "id": 1,
            "name": "技术分享",
            "description": "技术相关的文章",
            "articleCount": 15
        }
        // 更多分类记录...
    ],
    "message": ""
}
```

### 获取分类列表
- 接口功能：分页获取分类信息列表
- 接口地址: `/api/category/list`
- 方法: POST
- 需要登录: 是
- 请求参数:
```json
{
    "pageNum": 1,
    "pageSize": 10,
    "name": "分类名称（可选）"
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {
        "total": 10,
        "list": [
            {
                "id": 1,
                "name": "技术分享",
                "description": "技术相关的文章",
                "articleCount": 15,
                "createTime": "2024-01-01 10:00:00"
            }
            // 更多分类记录...
        ]
    },
    "message": ""
}
```

### 添加分类
- 接口功能：添加新分类
- 接口地址: `/api/category/add`
- 方法: POST
- 需要登录: 是
- 请求参数:
```json
{
    "name": "生活随笔",
    "description": "记录生活点滴"
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {
        "id": 2
    },
    "message": "添加成功"
}
```

### 更新分类
- 接口功能：更新分类信息
- 接口地址: `/api/category/update`
- 方法: POST
- 需要登录: 是
- 请求参数:
```json
{
    "id": 1,
    "name": "技术分享",
    "description": "技术相关的文章分享"
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {},
    "message": "更新成功"
}
```

### 删除分类
- 接口功能：删除分类
- 接口地址: `/api/category/delete`
- 方法: POST
- 需要登录: 是
- 请求参数:
```json
{
    "id": 1
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {},
    "message": "删除成功"
}
```

## 标签管理

### 获取所有标签列表
- 接口功能：获取所有标签信息列表（不分页）
- 接口地址: `/api/tag/list/all`
- 方法: GET
- 需要登录: 否
- 请求参数: 无
- 返回值:
```json
{
    "error": 0,
    "body": [
        {
            "id": 1,
            "name": "Java",
            "color": "#ff6b6b",
            "articleCount": 8
        }
        // 更多标签记录...
    ],
    "message": ""
}
```

### 获取标签列表
- 接口功能：分页获取标签信息列表
- 接口地址: `/api/tag/list`
- 方法: POST
- 需要登录: 是
- 请求参数:
```json
{
    "pageNum": 1,
    "pageSize": 10,
    "name": "标签名称（可选）"
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {
        "total": 20,
        "list": [
            {
                "id": 1,
                "name": "Java",
                "color": "#ff6b6b",
                "articleCount": 8,
                "createTime": "2024-01-01 10:00:00"
            }
            // 更多标签记录...
        ]
    },
    "message": ""
}
```

### 添加标签
- 接口功能：添加新标签
- 接口地址: `/api/tag/add`
- 方法: POST
- 需要登录: 是
- 请求参数:
```json
{
    "name": "Spring Boot",
    "color": "#4ecdc4"
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {
        "id": 2
    },
    "message": "添加成功"
}
```

### 更新标签
- 接口功能：更新标签信息
- 接口地址: `/api/tag/update`
- 方法: POST
- 需要登录: 是
- 请求参数:
```json
{
    "id": 1,
    "name": "Java",
    "color": "#ff6b6b"
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {},
    "message": "更新成功"
}
```

### 删除标签
- 接口功能：删除标签
- 接口地址: `/api/tag/delete`
- 方法: POST
- 需要登录: 是
- 请求参数:
```json
{
    "id": 1
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {},
    "message": "删除成功"
}
```

## 评论管理

### 获取文章评论列表
- 接口功能：获取指定文章的评论列表
- 接口地址: `/api/comment/list`
- 方法: POST
- 需要登录: 否
- 请求参数:
```json
{
    "articleId": 1,
    "pageNum": 1,
    "pageSize": 10
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {
        "total": 15,
        "list": [
            {
                "id": 1,
                "content": "写得很好，学到了！",
                "authorId": 2,
                "authorName": "读者A",
                "authorAvatar": "头像URL",
                "parentId": 0, // 0表示顶级评论
                "replyToUserId": 0,
                "replyToUserName": "",
                "likeCount": 3,
                "createTime": "2024-01-01 12:00:00",
                "replies": [
                    {
                        "id": 2,
                        "content": "谢谢支持！",
                        "authorId": 1,
                        "authorName": "博主",
                        "authorAvatar": "头像URL",
                        "parentId": 1,
                        "replyToUserId": 2,
                        "replyToUserName": "读者A",
                        "likeCount": 1,
                        "createTime": "2024-01-01 13:00:00"
                    }
                ]
            }
            // 更多评论记录...
        ]
    },
    "message": ""
}
```

### 发表评论
- 接口功能：对文章发表评论或回复
- 接口地址: `/api/comment/add`
- 方法: POST
- 需要登录: 是
- 请求参数:
```json
{
    "articleId": 1,
    "content": "评论内容",
    "parentId": 0, // 0表示顶级评论，其他值表示回复某条评论
    "replyToUserId": 0 // 回复的用户ID，顶级评论时为0
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {
        "id": 3
    },
    "message": "评论成功"
}
```

### 删除评论
- 接口功能：删除评论（仅作者和博主可删除）
- 接口地址: `/api/comment/delete`
- 方法: POST
- 需要登录: 是
- 请求参数:
```json
{
    "id": 1
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {},
    "message": "删除成功"
}
```

### 点赞评论
- 接口功能：对评论进行点赞或取消点赞
- 接口地址: `/api/comment/like`
- 方法: POST
- 需要登录: 是
- 请求参数:
```json
{
    "commentId": 1,
    "action": "like" // like/unlike
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {
        "likeCount": 4
    },
    "message": "操作成功"
}
```

## 用户管理

### 获取用户信息
- 接口功能：获取当前登录用户信息
- 接口地址: `/api/user/profile`
- 方法: GET
- 需要登录: 是
- 请求参数: 无
- 返回值:
```json
{
    "error": 0,
    "body": {
        "id": 1,
        "username": "blogger",
        "nickname": "博主昵称",
        "email": "<EMAIL>",
        "avatar": "头像URL",
        "bio": "个人简介",
        "website": "个人网站",
        "location": "所在地",
        "articleCount": 20,
        "followCount": 100,
        "fanCount": 50,
        "createTime": "2024-01-01 10:00:00"
    },
    "message": ""
}
```

### 更新用户信息
- 接口功能：更新用户个人信息
- 接口地址: `/api/user/update`
- 方法: POST
- 需要登录: 是
- 请求参数:
```json
{
    "nickname": "新昵称",
    "email": "<EMAIL>",
    "avatar": "新头像URL",
    "bio": "新的个人简介",
    "website": "新的个人网站",
    "location": "新的所在地"
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {},
    "message": "更新成功"
}
```

### 修改密码
- 接口功能：修改用户密码
- 接口地址: `/api/user/change-password`
- 方法: POST
- 需要登录: 是
- 请求参数:
```json
{
    "oldPassword": "旧密码",
    "newPassword": "新密码"
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {},
    "message": "密码修改成功"
}
```

### 获取用户公开信息
- 接口功能：根据用户ID获取用户公开信息
- 接口地址: `/api/user/public-info`
- 方法: POST
- 需要登录: 否
- 请求参数:
```json
{
    "userId": 1
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {
        "id": 1,
        "nickname": "博主昵称",
        "avatar": "头像URL",
        "bio": "个人简介",
        "website": "个人网站",
        "location": "所在地",
        "articleCount": 20,
        "followCount": 100,
        "fanCount": 50,
        "createTime": "2024-01-01 10:00:00"
    },
    "message": ""
}
```

## 文章互动

### 点赞文章
- 接口功能：对文章进行点赞或取消点赞
- 接口地址: `/api/article/like`
- 方法: POST
- 需要登录: 是
- 请求参数:
```json
{
    "articleId": 1,
    "action": "like" // like/unlike
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {
        "likeCount": 11
    },
    "message": "操作成功"
}
```

### 收藏文章
- 接口功能：收藏或取消收藏文章
- 接口地址: `/api/article/favorite`
- 方法: POST
- 需要登录: 是
- 请求参数:
```json
{
    "articleId": 1,
    "action": "favorite" // favorite/unfavorite
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {},
    "message": "操作成功"
}
```

### 获取收藏文章列表
- 接口功能：获取用户收藏的文章列表
- 接口地址: `/api/article/favorite-list`
- 方法: POST
- 需要登录: 是
- 请求参数:
```json
{
    "pageNum": 1,
    "pageSize": 10
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {
        "total": 5,
        "list": [
            {
                "id": 1,
                "title": "我的第一篇博客",
                "summary": "这是文章摘要...",
                "coverImage": "封面图片URL",
                "authorName": "博主昵称",
                "createTime": "2024-01-01 10:00:00",
                "favoriteTime": "2024-01-02 15:30:00"
            }
            // 更多收藏文章记录...
        ]
    },
    "message": ""
}
```

## 文件上传

### 上传图片
- 接口功能：上传图片文件（用于文章封面、用户头像等）
- 接口地址: `/api/upload/image`
- 方法: POST
- 需要登录: 是
- 请求参数: FormData格式
```
file: 图片文件
type: 图片类型（avatar/cover/content）
```
- 返回值:
```json
{
    "error": 0,
    "body": {
        "url": "https://example.com/uploads/images/20240101/image.jpg",
        "filename": "image.jpg",
        "size": 102400
    },
    "message": "上传成功"
}
```

## 搜索功能

### 搜索文章
- 接口功能：根据关键词搜索文章
- 接口地址: `/api/search/articles`
- 方法: POST
- 需要登录: 否
- 请求参数:
```json
{
    "keyword": "搜索关键词",
    "pageNum": 1,
    "pageSize": 10,
    "categoryId": 1, // 可选，限定分类
    "tagId": 1 // 可选，限定标签
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {
        "total": 8,
        "list": [
            {
                "id": 1,
                "title": "我的第一篇博客",
                "summary": "这是文章摘要...",
                "coverImage": "封面图片URL",
                "categoryName": "技术分享",
                "authorName": "博主昵称",
                "viewCount": 100,
                "createTime": "2024-01-01 10:00:00",
                "highlight": "高亮的匹配文本片段..."
            }
            // 更多搜索结果...
        ]
    },
    "message": ""
}
```

## 统计接口

### 获取博客统计信息
- 接口功能：获取博客系统的统计数据
- 接口地址: `/api/statistics/overview`
- 方法: GET
- 需要登录: 是
- 请求参数: 无
- 返回值:
```json
{
    "error": 0,
    "body": {
        "articleCount": 50,
        "categoryCount": 8,
        "tagCount": 25,
        "commentCount": 120,
        "viewCount": 5000,
        "likeCount": 300,
        "userCount": 100
    },
    "message": ""
}
```

### 获取文章访问统计
- 接口功能：获取文章访问量统计（按日期）
- 接口地址: `/api/statistics/article-views`
- 方法: POST
- 需要登录: 是
- 请求参数:
```json
{
    "startDate": "2024-01-01",
    "endDate": "2024-01-31",
    "articleId": 1 // 可选，不传则统计所有文章
}
```
- 返回值:
```json
{
    "error": 0,
    "body": {
        "dates": ["2024-01-01", "2024-01-02", "2024-01-03"],
        "views": [10, 15, 8]
    },
    "message": ""
}
```

### 获取热门文章
- 接口功能：获取热门文章列表
- 接口地址: `/api/statistics/hot-articles`
- 方法: POST
- 需要登录: 否
- 请求参数:
```json
{
    "limit": 10,
    "type": "view" // view/like/comment
}
```
- 返回值:
```json
{
    "error": 0,
    "body": [
        {
            "id": 1,
            "title": "我的第一篇博客",
            "viewCount": 500,
            "likeCount": 50,
            "commentCount": 20,
            "createTime": "2024-01-01 10:00:00"
        }
        // 更多热门文章...
    ],
    "message": ""
}
```

### 获取热门标签
- 接口功能：获取使用频率最高的标签
- 接口地址: `/api/statistics/hot-tags`
- 方法: GET
- 需要登录: 否
- 请求参数: 无
- 返回值:
```json
{
    "error": 0,
    "body": [
        {
            "id": 1,
            "name": "Java",
            "color": "#ff6b6b",
            "articleCount": 15
        }
        // 更多热门标签...
    ],
    "message": ""
}
```

## 系统配置

### 获取网站配置
- 接口功能：获取网站基本配置信息
- 接口地址: `/api/config/site`
- 方法: GET
- 需要登录: 否
- 请求参数: 无
- 返回值:
```json
{
    "error": 0,
    "body": {
        "siteName": "我的个人博客",
        "siteDescription": "记录技术与生活",
        "siteKeywords": "博客,技术,生活",
        "siteLogo": "网站Logo URL",
        "siteIcon": "网站图标URL",
        "authorName": "博主姓名",
        "authorEmail": "<EMAIL>",
        "icp": "备案号",
        "copyright": "版权信息"
    },
    "message": ""
}
```
